#pragma once

#include <QMainWindow>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QComboBox>
#include <QListWidget>
#include <QPushButton>
#include <QLabel>
#include <QGroupBox>
#include <QCheckBox>
#include <QStatusBar>
#include <QMenuBar>
#include <QFileDialog>
#include <QMessageBox>
#include <memory>

namespace arinc424 {

class NavigationData;
class FilterManager;
class MapWidget;
class ProcessManager;

/**
 * @brief Main application window
 */
class MainWindow : public QMainWindow {
    Q_OBJECT

public:
    explicit MainWindow(QWidget* parent = nullptr);
    ~MainWindow() = default;

private slots:
    // File operations
    void openJsonFile();
    void openArincFile();
    void browseProcessedFiles();
    void exitApplication();
    
    // Filter operations
    void onRegionChanged();
    void onIcaoChanged();
    void onRecordTypeSelectionChanged();
    void applyFilters();
    void clearFilters();
    
    // Display options
    void onShowAirportsChanged(bool show);
    void onShowWaypointsChanged(bool show);
    void onShowProceduresChanged(bool show);
    void onShowRouteLinesChanged(bool show);
    
    // Map operations
    void zoomToFit();
    void zoomIn();
    void zoomOut();
    void resetView();
    
    // Data events
    void onFiltersApplied();
    void onAirportClicked(const QString& icaoCode);
    void onWaypointClicked(const QString& identifier);
    void onProcedureClicked(const QString& identifier);

public:
    // Public method for command line loading
    void loadFileFromCommandLine(const QString& filePath);

public:
    // Public method for command line loading
    void loadFileFromCommandLine(const QString& filePath);

private:
    // Data management
    std::shared_ptr<NavigationData> navData_;
    std::shared_ptr<FilterManager> filterManager_;
    std::shared_ptr<ProcessManager> processManager_;
    
    // UI components
    MapWidget* mapWidget_;
    
    // Filter controls
    QGroupBox* filterGroupBox_;
    QComboBox* regionComboBox_;
    QComboBox* icaoComboBox_;
    QListWidget* recordTypeListWidget_;
    QPushButton* applyFiltersButton_;
    QPushButton* clearFiltersButton_;
    
    // Display controls
    QGroupBox* displayGroupBox_;
    QCheckBox* showAirportsCheckBox_;
    QCheckBox* showWaypointsCheckBox_;
    QCheckBox* showProceduresCheckBox_;
    QCheckBox* showRouteLinesCheckBox_;
    
    // Map controls
    QGroupBox* mapControlsGroupBox_;
    QPushButton* zoomToFitButton_;
    QPushButton* zoomInButton_;
    QPushButton* zoomOutButton_;
    QPushButton* resetViewButton_;
    
    // Status information
    QLabel* statusLabel_;
    QLabel* dataStatsLabel_;
    
    // Helper functions
    void setupUI();
    void setupMenuBar();
    void setupStatusBar();
    void setupFilterControls();
    void setupDisplayControls();
    void setupMapControls();
    void connectSignals();
    
    void updateFilterOptions();
    void updateStatusInfo();
    void showDataInfo();
    
    // File handling
    bool loadNavigationData(const QString& filePath);
    void processArincFile(const QString& filePath);
    void showProcessedFileBrowser();
};

} // namespace arinc424

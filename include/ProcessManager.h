#pragma once

#include <QObject>
#include <QProcess>
#include <QString>
#include <QTimer>
#include <QProgressDialog>
#include <memory>

namespace arinc424 {

/**
 * @brief Manages external process execution for ARINC 424 file processing
 */
class ProcessManager : public QObject {
    Q_OBJECT

public:
    explicit ProcessManager(QObject* parent = nullptr);
    ~ProcessManager() = default;

    // Process ARINC 424 .pc file to JSON (automatically determines output path)
    QString processArincFile(const QString& arincFilePath, QWidget* parentWidget = nullptr);

    // Check if Python script is available
    bool isPythonScriptAvailable() const;
    QString getPythonScriptPath() const;

    // Directory management
    QString getOutputDirectory() const;
    QStringList getAvailableJsonFiles() const;
    bool ensureOutputDirectoryExists();

    // Get last error message
    QString getLastError() const { return lastError_; }

signals:
    void processStarted();
    void processFinished(bool success);
    void processProgress(const QString& message);
    void processError(const QString& error);

private slots:
    void onProcessFinished(int exitCode, QProcess::ExitStatus exitStatus);
    void onProcessError(QProcess::ProcessError error);
    void onProcessOutput();
    void onProgressTimeout();

private:
    std::unique_ptr<QProcess> process_;
    QProgressDialog* progressDialog_;
    QTimer* progressTimer_;
    QString lastError_;
    QString currentOutputPath_;
    
    // Helper functions
    QString findPythonExecutable() const;
    QString findPythonScript() const;
    bool validatePythonEnvironment() const;
    void setupProgressDialog(QWidget* parent, const QString& arincFilePath);
    void cleanupProcess();
    
    // Helper functions for output directory management
    QString generateOutputFileName(const QString& arincFilePath) const;

    // Constants
    static constexpr int PROCESS_TIMEOUT_MS = 300000; // 5 minutes
    static constexpr int PROGRESS_UPDATE_MS = 500;    // 0.5 seconds
    static constexpr const char* OUTPUT_SUBDIR = "processed_json";
};

} // namespace arinc424

#pragma once

#include <QObject>
#include <QString>
#include <QStringList>
#include <memory>

namespace arinc424 {

class NavigationData;
class Airport;
class Waypoint;
class SIDSTARApproach;

/**
 * @brief Manages hierarchical filtering of navigation data
 * 
 * Provides a three-level hierarchy: Region -> ICAO -> Record Type
 * Handles cross-referencing of related records (e.g., SIDs/STARs referencing waypoints)
 */
class FilterManager : public QObject {
    Q_OBJECT

public:
    explicit FilterManager(QObject* parent = nullptr);
    ~FilterManager() = default;

    // Set the navigation data source
    void setNavigationData(std::shared_ptr<NavigationData> navData);

    // Get available filter options
    QStringList getAvailableRegions() const;
    QStringList getAvailableICAOCodes(const QString& region = QString()) const;
    QStringList getAvailableRecordTypes(const QString& icaoCode = QString()) const;

    // Current filter state
    QString getCurrentRegion() const { return currentRegion_; }
    QString getCurrentICAOCode() const { return currentIcaoCode_; }
    QStringList getCurrentRecordTypes() const { return currentRecordTypes_; }

    // Apply filters (single-shot operation)
    void applyFilters(const QString& region, const QString& icaoCode, const QStringList& recordTypes);
    void clearFilters();

    // Get filtered results
    std::vector<std::shared_ptr<Airport>> getFilteredAirports() const;
    std::vector<std::shared_ptr<Waypoint>> getFilteredWaypoints() const;
    std::vector<std::shared_ptr<SIDSTARApproach>> getFilteredProcedures() const;

    // Get all related records (includes referenced waypoints for procedures)
    std::vector<std::shared_ptr<Waypoint>> getAllRelatedWaypoints() const;

    // Statistics
    int getFilteredAirportCount() const;
    int getFilteredWaypointCount() const;
    int getFilteredProcedureCount() const;

signals:
    // Emitted when filters are applied
    void filtersApplied();
    
    // Emitted when available options change
    void availableRegionsChanged();
    void availableICAOCodesChanged();
    void availableRecordTypesChanged();

private slots:
    void onDataChanged();

private:
    std::shared_ptr<NavigationData> navData_;
    
    // Current filter state
    QString currentRegion_;
    QString currentIcaoCode_;
    QStringList currentRecordTypes_;
    
    // Cached filtered results
    mutable std::vector<std::shared_ptr<Airport>> filteredAirports_;
    mutable std::vector<std::shared_ptr<Waypoint>> filteredWaypoints_;
    mutable std::vector<std::shared_ptr<SIDSTARApproach>> filteredProcedures_;
    mutable bool resultsValid_ = false;
    
    // Helper functions
    void invalidateResults();
    void updateFilteredResults() const;
    bool matchesFilter(std::shared_ptr<Airport> airport) const;
    bool matchesFilter(std::shared_ptr<Waypoint> waypoint) const;
    bool matchesFilter(std::shared_ptr<SIDSTARApproach> procedure) const;
    
    // Cross-reference helpers
    std::vector<std::shared_ptr<Waypoint>> getReferencedWaypoints(const std::vector<std::shared_ptr<SIDSTARApproach>>& procedures) const;
};

} // namespace arinc424

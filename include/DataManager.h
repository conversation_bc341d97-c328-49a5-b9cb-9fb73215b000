#pragma once

#include <QObject>
#include <QString>
#include <memory>

namespace arinc424 {

class NavigationData;

/**
 * @brief Manages loading and caching of navigation data
 */
class DataManager : public QObject {
    Q_OBJECT

public:
    explicit DataManager(QObject* parent = nullptr);
    ~DataManager() = default;

    // Data loading
    bool loadFromJsonFile(const QString& filePath);
    bool loadFromJsonString(const QString& jsonString);
    
    // Data access
    std::shared_ptr<NavigationData> getNavigationData() const { return navData_; }
    
    // Status
    bool hasData() const;
    QString getLastError() const { return lastError_; }
    
    // Statistics
    int getAirportCount() const;
    int getWaypointCount() const;
    int getProcedureCount() const;

signals:
    void dataLoaded();
    void dataCleared();
    void loadingError(const QString& error);

public slots:
    void clearData();

private:
    std::shared_ptr<NavigationData> navData_;
    QString lastError_;
    
    void setError(const QString& error);
};

} // namespace arinc424

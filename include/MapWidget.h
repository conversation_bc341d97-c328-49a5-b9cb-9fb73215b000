#pragma once

#include <QWidget>
#include <QPainter>
#include <QMouseEvent>
#include <QWheelEvent>
#include <QTimer>
#include <memory>
#include <vector>

namespace arinc424 {

class NavigationData;
class FilterManager;
class Airport;
class Waypoint;
class SIDSTARApproach;

namespace utils {
class CoordinateConverter;
}

/**
 * @brief Widget for displaying navigation data on a map
 */
class MapWidget : public QWidget {
    Q_OBJECT

public:
    explicit MapWidget(QWidget* parent = nullptr);
    ~MapWidget();

    // Set data sources
    void setNavigationData(std::shared_ptr<NavigationData> navData);
    void setFilterManager(std::shared_ptr<FilterManager> filterManager);

    // Display options
    void setShowAirports(bool show) { showAirports_ = show; update(); }
    void setShowWaypoints(bool show) { showWaypoints_ = show; update(); }
    void setShowProcedures(bool show) { showProcedures_ = show; update(); }
    void setShowRouteLines(bool show) { showRouteLines_ = show; update(); }

    bool getShowAirports() const { return showAirports_; }
    bool getShowWaypoints() const { return showWaypoints_; }
    bool getShowProcedures() const { return showProcedures_; }
    bool getShowRouteLines() const { return showRouteLines_; }

    // Map operations
    void zoomToFit();
    void zoomIn();
    void zoomOut();
    void resetView();

public slots:
    void onFiltersApplied();
    void onDataChanged();

signals:
    void airportClicked(const QString& icaoCode);
    void waypointClicked(const QString& identifier);
    void procedureClicked(const QString& identifier);

protected:
    // Qt event handlers
    void paintEvent(QPaintEvent* event) override;
    void mousePressEvent(QMouseEvent* event) override;
    void mouseMoveEvent(QMouseEvent* event) override;
    void mouseReleaseEvent(QMouseEvent* event) override;
    void wheelEvent(QWheelEvent* event) override;
    void resizeEvent(QResizeEvent* event) override;

private slots:
    void onUpdateTimer();

private:
    // Data sources
    std::shared_ptr<NavigationData> navData_;
    std::shared_ptr<FilterManager> filterManager_;
    std::unique_ptr<utils::CoordinateConverter> converter_;

    // Display options
    bool showAirports_ = true;
    bool showWaypoints_ = true;
    bool showProcedures_ = true;
    bool showRouteLines_ = true;

    // Mouse interaction
    bool isDragging_ = false;
    QPoint lastMousePos_;
    QPoint dragStartPos_;

    // Update timer for smooth interaction
    QTimer* updateTimer_;
    bool needsUpdate_ = false;

    // Cached data for rendering
    std::vector<std::shared_ptr<Airport>> visibleAirports_;
    std::vector<std::shared_ptr<Waypoint>> visibleWaypoints_;
    std::vector<std::shared_ptr<SIDSTARApproach>> visibleProcedures_;
    bool cacheValid_ = false;

    // Drawing functions
    void drawBackground(QPainter& painter);
    void drawGrid(QPainter& painter);
    void drawAirports(QPainter& painter);
    void drawWaypoints(QPainter& painter);
    void drawProcedures(QPainter& painter);
    void drawRouteLines(QPainter& painter);
    void drawProcedureRoute(QPainter& painter, std::shared_ptr<SIDSTARApproach> procedure);
    void drawProcedureRoute(QPainter& painter, const std::vector<std::shared_ptr<SIDSTARApproach>>& procedures);

    // Helper functions
    void updateVisibleData();
    void invalidateCache();
    void setupCoordinateConverter();
    QPointF getScreenPosition(double latitude, double longitude) const;
    QPointF getGeoPosition(const QPoint& screenPos) const;
    
    // Hit testing
    std::shared_ptr<Airport> findAirportAt(const QPoint& pos) const;
    std::shared_ptr<Waypoint> findWaypointAt(const QPoint& pos) const;
    std::shared_ptr<SIDSTARApproach> findProcedureAt(const QPoint& pos) const;

    // Style constants
    static constexpr int AIRPORT_SYMBOL_SIZE = 8;
    static constexpr int WAYPOINT_SYMBOL_SIZE = 6;
    static constexpr int PROCEDURE_SYMBOL_SIZE = 4;
    static constexpr int HIT_TEST_RADIUS = 10;
    static constexpr double ZOOM_FACTOR = 1.5;
};

} // namespace arinc424

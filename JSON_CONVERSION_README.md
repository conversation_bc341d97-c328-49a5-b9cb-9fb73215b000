# ARINC-424 to JSON Conversion Tools

This document describes the tools and scripts created to parse ARINC-424 navigation data files and convert them to JSON format.

## Overview

ARINC-424 is the international standard file format for aircraft navigation data. This library provides Python tools to parse these files and convert the structured data into JSON format for easier processing and integration with modern applications.

## Files Created

### 1. `arinc424_to_json.py` - Command Line Tool

A comprehensive command-line tool for converting ARINC-424 data to JSON format.

**Features:**
- Parse single ARINC-424 records
- Parse entire files containing multiple records
- Parse directories with multiple ARINC-424 files
- Output to stdout or save to JSON files
- Pretty-printed or compact JSON output
- Progress reporting and error handling
- Metadata inclusion (source file, line numbers, raw records)

**Usage Examples:**

```bash
# Parse a single record
python3 arinc424_to_json.py --record "SUSAP KSEAK1ASEA     110000119Y *********W122182910E019900429250SEA K11800018000CU00Y NAS    SEATTLE-TACOMA INTL           045698808"

# Parse a single file and print to stdout
python3 arinc424_to_json.py --file data/ARINC-424-18/airport

# Parse a single file and save to JSON file
python3 arinc424_to_json.py --file data/ARINC-424-18/airport --output airport_data.json

# Parse all files in a directory
python3 arinc424_to_json.py --directory data/ARINC-424-18 --output all_data.json

# Parse with compact JSON output (no pretty printing)
python3 arinc424_to_json.py --file data/ARINC-424-18/airport --compact

# Parse quietly (suppress progress messages)
python3 arinc424_to_json.py --file data/ARINC-424-18/airport --quiet
```

**Command Line Options:**
- `--record, -r`: Parse a single ARINC-424 record string
- `--file, -f`: Parse an ARINC-424 file
- `--directory, -d`: Parse all ARINC-424 files in a directory
- `--output, -o`: Output JSON file path (default: print to stdout)
- `--compact, -c`: Output compact JSON (no pretty printing)
- `--quiet, -q`: Suppress progress messages

### 2. `example_usage.py` - Programming Examples

A demonstration script showing how to use the ARINC-424 library programmatically.

**Features:**
- Examples of parsing single records
- File processing examples
- Different record type handling
- JSON conversion techniques
- Error handling patterns

**Run the examples:**
```bash
python3 example_usage.py
```

## JSON Output Format

### Single Record Output
When parsing a single record, the output is a JSON object with all parsed fields:

```json
{
  "Record Type": "S",
  "Customer / Area Code": "USA",
  "Section Code": "PA",
  "Airport ICAO Identifier": "KSEA",
  "Airport Name": "SEATTLE-TACOMA INTL           ",
  "Airport Reference Pt. Latitude": "*********",
  "Airport Reference Pt. Longitude": "W122182910",
  "Airport Elevation": "00429",
  ...
}
```

### File Output
When parsing a file, the output is an array of record objects with additional metadata:

```json
[
  {
    "Record Type": "S",
    "Airport ICAO Identifier": "KSEA",
    "Airport Name": "SEATTLE-TACOMA INTL           ",
    "_source_file": "airport",
    "_line_number": 7,
    "_raw_record": "SUSAP KSEAK1ASEA     110000119Y...",
    ...
  }
]
```

### Directory Output
When parsing a directory, the output is a nested object with filenames as keys:

```json
{
  "airport": [
    { "Record Type": "S", ... },
    { "Record Type": "S", ... }
  ],
  "navaid_vhf": [
    { "Record Type": "S", ... },
    { "Record Type": "S", ... }
  ],
  ...
}
```

## Installation and Setup

1. **Install the library in development mode:**
   ```bash
   cd /path/to/arinc424-main
   python3 -m pip install -e .
   ```

2. **Verify installation:**
   ```bash
   python3 -c "import arinc424; print('ARINC-424 library installed successfully')"
   ```

3. **Make scripts executable:**
   ```bash
   chmod +x arinc424_to_json.py
   chmod +x example_usage.py
   ```

## Sample Data

The repository includes sample ARINC-424 data files in the `data/` directory:

- `data/ARINC-424-18/`: Contains various types of navigation data files
  - `airport`: Airport reference points
  - `navaid_vhf`: VHF navigation aids
  - `waypoint`: Waypoints
  - `runway`: Runway information
  - And many more...

## Supported Record Types

The library supports parsing various ARINC-424 record types including:

- **PA**: Airport Reference Points
- **PG**: Runways
- **D **: VHF Navigation Aids
- **DB**: NDB Navigation Aids
- **EA**: Enroute Waypoints
- **PC**: Terminal Waypoints
- **PD/PE/PF**: SID/STAR/Approach Procedures
- **ER**: Enroute Airways
- **UC**: Controlled Airspace
- **UR**: Restrictive Airspace
- And many more...

## Error Handling

The tools include comprehensive error handling:

- Invalid record format detection
- File not found errors
- Parsing failures with line number reporting
- Graceful handling of unsupported record types
- Progress reporting for large files

## Performance Considerations

- The tools process files line by line to handle large datasets efficiently
- Memory usage scales with the number of successfully parsed records
- For very large directories, consider processing files individually
- Use the `--quiet` flag to reduce output overhead for batch processing

## Integration Examples

### Using in Python Scripts

```python
import json
import arinc424

# Parse a single record
record = arinc424.Record()
if record.read(arinc424_line):
    json_data = record.json(output=False)
    parsed_data = json.loads(json_data)
    # Process parsed_data...

# Parse a file
records = []
with open('navigation_data.txt', 'r') as f:
    for line in f:
        record = arinc424.Record()
        if record.read(line.strip()):
            json_data = record.json(output=False)
            records.append(json.loads(json_data))
```

### Using with Other Tools

The JSON output can be easily integrated with:
- Databases (MongoDB, PostgreSQL JSON columns)
- Web APIs and REST services
- Data analysis tools (pandas, numpy)
- Visualization libraries
- GIS applications

## Troubleshooting

**Common Issues:**

1. **Import Error**: Make sure the library is installed with `pip install -e .`
2. **File Not Found**: Check file paths are relative to the current working directory
3. **Parsing Failures**: Some lines may be comments or invalid records - this is normal
4. **Memory Issues**: For very large files, consider processing in chunks

**Getting Help:**
```bash
python3 arinc424_to_json.py --help
```

## Next Steps

- Extend the tools to support additional output formats (CSV, XML)
- Add filtering capabilities for specific record types
- Implement data validation and quality checks
- Add geographic coordinate conversion utilities
- Create web interface for file uploads and conversion

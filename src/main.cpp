#include <QApplication>
#include <QStyleFactory>
#include <QDir>
#include <QStandardPaths>
#include <QMessageBox>
#include <QCommandLineParser>
#include <QCommandLineOption>
#include <QTimer>
#include <QFile>
#include "MainWindow.h"

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    // Set application properties
    app.setApplicationName("ARINC 424 Map Viewer");
    app.setApplicationVersion("1.0.0");
    app.setOrganizationName("ARINC 424 Tools");
    app.setApplicationDisplayName("ARINC 424 Map Viewer");
    
    // Set up command line parser
    QCommandLineParser parser;
    parser.setApplicationDescription("Interactive map viewer for ARINC 424 navigation data");
    parser.addHelpOption();
    parser.addVersionOption();
    
    QCommandLineOption jsonFileOption(QStringList() << "f" << "file",
                                     "JSON file to load on startup",
                                     "file");
    parser.addOption(jsonFileOption);
    
    // Process command line arguments
    parser.process(app);
    
    try {
        // Create and show main window
        arinc424::MainWindow window;
        window.show();
        
        // Load file if specified on command line
        if (parser.isSet(jsonFileOption)) {
            QString jsonFile = parser.value(jsonFileOption);
            if (QFile::exists(jsonFile)) {
                // Load the file programmatically
                QTimer::singleShot(100, [&window, jsonFile]() {
                    window.loadFileFromCommandLine(jsonFile);
                });
            } else {
                QMessageBox::warning(&window, "File Not Found",
                                   QString("The specified file does not exist: %1").arg(jsonFile));
            }
        }
        
        return app.exec();
        
    } catch (const std::exception& e) {
        QMessageBox::critical(nullptr, "Fatal Error", 
                             QString("An unexpected error occurred: %1").arg(e.what()));
        return 1;
    } catch (...) {
        QMessageBox::critical(nullptr, "Fatal Error", 
                             "An unknown error occurred.");
        return 1;
    }
}

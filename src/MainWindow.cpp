#include "MainWindow.h"
#include "MapWidget.h"
#include "FilterManager.h"
#include "ProcessManager.h"
#include "models/NavigationData.h"
#include <QApplication>
#include <QSplitter>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QComboBox>
#include <QListWidget>
#include <QPushButton>
#include <QLabel>
#include <QGroupBox>
#include <QCheckBox>
#include <QStatusBar>
#include <QMenuBar>
#include <QFileDialog>
#include <QMessageBox>
#include <QStandardPaths>
#include <QDialog>
#include <QListWidget>
#include <QListWidgetItem>
#include <QFileInfo>

namespace arinc424 {

MainWindow::MainWindow(QWidget* parent)
    : QMainWindow(parent)
    , navData_(std::make_shared<NavigationData>())
    , filterManager_(std::make_shared<FilterManager>(this))
    , processManager_(std::make_shared<ProcessManager>(this))
{
    setWindowTitle("ARINC 424 Map Viewer");
    setMinimumSize(1200, 800);
    
    setupUI();
    setupMenuBar();
    setupStatusBar();
    connectSignals();
    
    // Initialize filter manager with navigation data
    filterManager_->setNavigationData(navData_);
    
    updateStatusInfo();
}

void MainWindow::setupUI() {
    auto centralWidget = new QWidget(this);
    setCentralWidget(centralWidget);
    
    // Create main splitter
    auto mainSplitter = new QSplitter(Qt::Horizontal, this);
    
    // Create left panel for controls
    auto leftPanel = new QWidget();
    leftPanel->setMaximumWidth(300);
    leftPanel->setMinimumWidth(250);
    
    auto leftLayout = new QVBoxLayout(leftPanel);
    
    // Setup control groups
    setupFilterControls();
    setupDisplayControls();
    setupMapControls();
    
    leftLayout->addWidget(filterGroupBox_);
    leftLayout->addWidget(displayGroupBox_);
    leftLayout->addWidget(mapControlsGroupBox_);
    leftLayout->addStretch();
    
    // Create map widget
    mapWidget_ = new MapWidget();
    mapWidget_->setNavigationData(navData_);
    mapWidget_->setFilterManager(filterManager_);
    
    // Add to splitter
    mainSplitter->addWidget(leftPanel);
    mainSplitter->addWidget(mapWidget_);
    mainSplitter->setStretchFactor(0, 0);
    mainSplitter->setStretchFactor(1, 1);
    
    // Set main layout
    auto mainLayout = new QHBoxLayout(centralWidget);
    mainLayout->addWidget(mainSplitter);
    mainLayout->setContentsMargins(5, 5, 5, 5);
}

void MainWindow::setupMenuBar() {
    auto fileMenu = menuBar()->addMenu("&File");

    auto openJsonAction = fileMenu->addAction("Open &JSON File...");
    openJsonAction->setShortcut(QKeySequence::Open);
    connect(openJsonAction, &QAction::triggered, this, &MainWindow::openJsonFile);

    auto openArincAction = fileMenu->addAction("Open &ARINC File (.pc)...");
    openArincAction->setShortcut(QKeySequence("Ctrl+Shift+O"));
    connect(openArincAction, &QAction::triggered, this, &MainWindow::openArincFile);

    fileMenu->addSeparator();

    auto browseProcessedAction = fileMenu->addAction("&Browse Processed Files...");
    browseProcessedAction->setShortcut(QKeySequence("Ctrl+B"));
    connect(browseProcessedAction, &QAction::triggered, this, &MainWindow::browseProcessedFiles);

    fileMenu->addSeparator();

    auto exitAction = fileMenu->addAction("E&xit");
    exitAction->setShortcut(QKeySequence::Quit);
    connect(exitAction, &QAction::triggered, this, &MainWindow::exitApplication);
    
    auto viewMenu = menuBar()->addMenu("&View");
    
    auto zoomToFitAction = viewMenu->addAction("Zoom to &Fit");
    zoomToFitAction->setShortcut(QKeySequence("Ctrl+0"));
    connect(zoomToFitAction, &QAction::triggered, this, &MainWindow::zoomToFit);
    
    auto zoomInAction = viewMenu->addAction("Zoom &In");
    zoomInAction->setShortcut(QKeySequence::ZoomIn);
    connect(zoomInAction, &QAction::triggered, this, &MainWindow::zoomIn);
    
    auto zoomOutAction = viewMenu->addAction("Zoom &Out");
    zoomOutAction->setShortcut(QKeySequence::ZoomOut);
    connect(zoomOutAction, &QAction::triggered, this, &MainWindow::zoomOut);
    
    viewMenu->addSeparator();
    
    auto resetViewAction = viewMenu->addAction("&Reset View");
    resetViewAction->setShortcut(QKeySequence("Ctrl+R"));
    connect(resetViewAction, &QAction::triggered, this, &MainWindow::resetView);
}

void MainWindow::setupStatusBar() {
    statusLabel_ = new QLabel("Ready");
    dataStatsLabel_ = new QLabel("No data loaded");
    
    statusBar()->addWidget(statusLabel_);
    statusBar()->addPermanentWidget(dataStatsLabel_);
}

void MainWindow::setupFilterControls() {
    filterGroupBox_ = new QGroupBox("Filters");
    auto layout = new QGridLayout(filterGroupBox_);
    
    // Region filter
    layout->addWidget(new QLabel("Region:"), 0, 0);
    regionComboBox_ = new QComboBox();
    regionComboBox_->addItem("All Regions", "");
    layout->addWidget(regionComboBox_, 0, 1);
    
    // ICAO filter
    layout->addWidget(new QLabel("ICAO:"), 1, 0);
    icaoComboBox_ = new QComboBox();
    icaoComboBox_->addItem("All ICAOs", "");
    layout->addWidget(icaoComboBox_, 1, 1);
    
    // Record type filter
    layout->addWidget(new QLabel("Record Types:"), 2, 0, 1, 2);
    recordTypeListWidget_ = new QListWidget();
    recordTypeListWidget_->setSelectionMode(QAbstractItemView::MultiSelection);
    recordTypeListWidget_->setMaximumHeight(120);
    layout->addWidget(recordTypeListWidget_, 3, 0, 1, 2);
    
    // Filter buttons
    auto buttonLayout = new QHBoxLayout();
    applyFiltersButton_ = new QPushButton("Apply");
    clearFiltersButton_ = new QPushButton("Clear");
    buttonLayout->addWidget(applyFiltersButton_);
    buttonLayout->addWidget(clearFiltersButton_);
    layout->addLayout(buttonLayout, 4, 0, 1, 2);
}

void MainWindow::setupDisplayControls() {
    displayGroupBox_ = new QGroupBox("Display Options");
    auto layout = new QVBoxLayout(displayGroupBox_);
    
    showAirportsCheckBox_ = new QCheckBox("Show Airports");
    showAirportsCheckBox_->setChecked(true);
    layout->addWidget(showAirportsCheckBox_);
    
    showWaypointsCheckBox_ = new QCheckBox("Show Waypoints");
    showWaypointsCheckBox_->setChecked(true);
    layout->addWidget(showWaypointsCheckBox_);
    
    showProceduresCheckBox_ = new QCheckBox("Show Procedures");
    showProceduresCheckBox_->setChecked(true);
    layout->addWidget(showProceduresCheckBox_);
    
    showRouteLinesCheckBox_ = new QCheckBox("Show Route Lines");
    showRouteLinesCheckBox_->setChecked(true);
    layout->addWidget(showRouteLinesCheckBox_);
}

void MainWindow::setupMapControls() {
    mapControlsGroupBox_ = new QGroupBox("Map Controls");
    auto layout = new QVBoxLayout(mapControlsGroupBox_);
    
    zoomToFitButton_ = new QPushButton("Zoom to Fit");
    zoomInButton_ = new QPushButton("Zoom In");
    zoomOutButton_ = new QPushButton("Zoom Out");
    resetViewButton_ = new QPushButton("Reset View");
    
    layout->addWidget(zoomToFitButton_);
    layout->addWidget(zoomInButton_);
    layout->addWidget(zoomOutButton_);
    layout->addWidget(resetViewButton_);
}

void MainWindow::connectSignals() {
    // Filter controls
    connect(regionComboBox_, QOverload<int>::of(&QComboBox::currentIndexChanged),
            this, &MainWindow::onRegionChanged);
    connect(icaoComboBox_, QOverload<int>::of(&QComboBox::currentIndexChanged),
            this, &MainWindow::onIcaoChanged);
    connect(recordTypeListWidget_, &QListWidget::itemSelectionChanged,
            this, &MainWindow::onRecordTypeSelectionChanged);
    connect(applyFiltersButton_, &QPushButton::clicked, this, &MainWindow::applyFilters);
    connect(clearFiltersButton_, &QPushButton::clicked, this, &MainWindow::clearFilters);

    // Display controls
    connect(showAirportsCheckBox_, &QCheckBox::toggled, this, &MainWindow::onShowAirportsChanged);
    connect(showWaypointsCheckBox_, &QCheckBox::toggled, this, &MainWindow::onShowWaypointsChanged);
    connect(showProceduresCheckBox_, &QCheckBox::toggled, this, &MainWindow::onShowProceduresChanged);
    connect(showRouteLinesCheckBox_, &QCheckBox::toggled, this, &MainWindow::onShowRouteLinesChanged);

    // Map controls
    connect(zoomToFitButton_, &QPushButton::clicked, this, &MainWindow::zoomToFit);
    connect(zoomInButton_, &QPushButton::clicked, this, &MainWindow::zoomIn);
    connect(zoomOutButton_, &QPushButton::clicked, this, &MainWindow::zoomOut);
    connect(resetViewButton_, &QPushButton::clicked, this, &MainWindow::resetView);

    // Filter manager
    connect(filterManager_.get(), &FilterManager::filtersApplied, this, &MainWindow::onFiltersApplied);

    // Map widget
    connect(mapWidget_, &MapWidget::airportClicked, this, &MainWindow::onAirportClicked);
    connect(mapWidget_, &MapWidget::waypointClicked, this, &MainWindow::onWaypointClicked);
    connect(mapWidget_, &MapWidget::procedureClicked, this, &MainWindow::onProcedureClicked);
}

void MainWindow::openJsonFile() {
    QString fileName = QFileDialog::getOpenFileName(
        this,
        "Open ARINC 424 JSON File",
        QStandardPaths::writableLocation(QStandardPaths::DocumentsLocation),
        "JSON Files (*.json);;All Files (*)"
    );

    if (!fileName.isEmpty()) {
        if (loadNavigationData(fileName)) {
            statusLabel_->setText("Data loaded successfully");
            updateFilterOptions();
            updateStatusInfo();
            mapWidget_->zoomToFit();
        } else {
            QMessageBox::warning(this, "Error", "Failed to load navigation data from file.");
            statusLabel_->setText("Failed to load data");
        }
    }
}

void MainWindow::openArincFile() {
    QString fileName = QFileDialog::getOpenFileName(
        this,
        "Open ARINC 424 File",
        QStandardPaths::writableLocation(QStandardPaths::DocumentsLocation),
        "ARINC 424 Files (*.pc);;All Files (*)"
    );

    if (!fileName.isEmpty()) {
        processArincFile(fileName);
    }
}

void MainWindow::browseProcessedFiles() {
    showProcessedFileBrowser();
}

void MainWindow::exitApplication() {
    QApplication::quit();
}

void MainWindow::onRegionChanged() {
    // Update ICAO combo box based on selected region
    QString selectedRegion = regionComboBox_->currentData().toString();

    icaoComboBox_->clear();
    icaoComboBox_->addItem("All ICAOs", "");

    auto icaoCodes = filterManager_->getAvailableICAOCodes(selectedRegion);
    for (const auto& icao : icaoCodes) {
        icaoComboBox_->addItem(icao, icao);
    }
}

void MainWindow::onIcaoChanged() {
    // Update record type list based on selected ICAO
    QString selectedIcao = icaoComboBox_->currentData().toString();

    recordTypeListWidget_->clear();

    auto recordTypes = filterManager_->getAvailableRecordTypes(selectedIcao);
    for (const auto& type : recordTypes) {
        auto item = new QListWidgetItem(type);
        item->setFlags(item->flags() | Qt::ItemIsUserCheckable);
        item->setCheckState(Qt::Unchecked);
        recordTypeListWidget_->addItem(item);
    }
}

void MainWindow::onRecordTypeSelectionChanged() {
    // Enable/disable apply button based on selection
    bool hasSelection = false;
    for (int i = 0; i < recordTypeListWidget_->count(); ++i) {
        if (recordTypeListWidget_->item(i)->checkState() == Qt::Checked) {
            hasSelection = true;
            break;
        }
    }
    // Apply button is always enabled for now
}

void MainWindow::applyFilters() {
    QString region = regionComboBox_->currentData().toString();
    QString icao = icaoComboBox_->currentData().toString();

    QStringList recordTypes;
    for (int i = 0; i < recordTypeListWidget_->count(); ++i) {
        auto item = recordTypeListWidget_->item(i);
        if (item->checkState() == Qt::Checked) {
            recordTypes.append(item->text());
        }
    }

    filterManager_->applyFilters(region, icao, recordTypes);
}

void MainWindow::clearFilters() {
    regionComboBox_->setCurrentIndex(0);
    icaoComboBox_->setCurrentIndex(0);

    for (int i = 0; i < recordTypeListWidget_->count(); ++i) {
        recordTypeListWidget_->item(i)->setCheckState(Qt::Unchecked);
    }

    filterManager_->clearFilters();
}

void MainWindow::onShowAirportsChanged(bool show) {
    mapWidget_->setShowAirports(show);
}

void MainWindow::onShowWaypointsChanged(bool show) {
    mapWidget_->setShowWaypoints(show);
}

void MainWindow::onShowProceduresChanged(bool show) {
    mapWidget_->setShowProcedures(show);
}

void MainWindow::onShowRouteLinesChanged(bool show) {
    mapWidget_->setShowRouteLines(show);
}

void MainWindow::zoomToFit() {
    mapWidget_->zoomToFit();
}

void MainWindow::zoomIn() {
    mapWidget_->zoomIn();
}

void MainWindow::zoomOut() {
    mapWidget_->zoomOut();
}

void MainWindow::resetView() {
    mapWidget_->resetView();
}

void MainWindow::onFiltersApplied() {
    updateStatusInfo();
}

void MainWindow::onAirportClicked(const QString& icaoCode) {
    statusLabel_->setText(QString("Clicked airport: %1").arg(icaoCode));
}

void MainWindow::onWaypointClicked(const QString& identifier) {
    statusLabel_->setText(QString("Clicked waypoint: %1").arg(identifier));
}

void MainWindow::onProcedureClicked(const QString& identifier) {
    statusLabel_->setText(QString("Clicked procedure: %1").arg(identifier));
}

void MainWindow::updateFilterOptions() {
    // Update region combo box
    regionComboBox_->clear();
    regionComboBox_->addItem("All Regions", "");

    auto regions = filterManager_->getAvailableRegions();
    for (const auto& region : regions) {
        regionComboBox_->addItem(region, region);
    }

    // Trigger update of dependent combo boxes
    onRegionChanged();
}

void MainWindow::updateStatusInfo() {
    if (!navData_) {
        dataStatsLabel_->setText("No data loaded");
        return;
    }

    int totalAirports = static_cast<int>(navData_->getAirportCount());
    int totalWaypoints = static_cast<int>(navData_->getWaypointCount());
    int totalProcedures = static_cast<int>(navData_->getSIDSTARCount());

    int filteredAirports = filterManager_->getFilteredAirportCount();
    int filteredWaypoints = filterManager_->getFilteredWaypointCount();
    int filteredProcedures = filterManager_->getFilteredProcedureCount();

    QString statsText = QString("Airports: %1/%2, Waypoints: %3/%4, Procedures: %5/%6")
                       .arg(filteredAirports).arg(totalAirports)
                       .arg(filteredWaypoints).arg(totalWaypoints)
                       .arg(filteredProcedures).arg(totalProcedures);

    dataStatsLabel_->setText(statsText);
}

bool MainWindow::loadNavigationData(const QString& filePath) {
    bool success = navData_->loadFromJson(filePath.toStdString());

    if (success) {
        // Update the filter manager with the new data
        filterManager_->setNavigationData(navData_);
    }

    return success;
}

void MainWindow::processArincFile(const QString& filePath) {
    // Check if Python environment is available
    if (!processManager_->isPythonScriptAvailable()) {
        QMessageBox::critical(this, "Python Environment Error",
                             "Python environment is not properly configured or the ARINC 424 parsing script is not available.\n\n"
                             "Please ensure:\n"
                             "1. Python 3 is installed and in PATH\n"
                             "2. The arinc424 Python module is installed\n"
                             "3. The arinc424_to_json.py script is in the application directory");
        return;
    }

    statusLabel_->setText("Processing ARINC file...");

    // Process the file
    QString outputJsonPath = processManager_->processArincFile(filePath, this);

    if (!outputJsonPath.isEmpty()) {
        // Successfully processed, now load the JSON
        if (loadNavigationData(outputJsonPath)) {
            statusLabel_->setText(QString("ARINC file processed and loaded successfully"));
            updateFilterOptions();
            updateStatusInfo();
            mapWidget_->zoomToFit();

            QMessageBox::information(this, "Success",
                                   QString("ARINC file processed successfully!\n\nOutput saved to:\n%1")
                                   .arg(outputJsonPath));
        } else {
            QMessageBox::warning(this, "Error", "ARINC file was processed but failed to load the resulting JSON data.");
            statusLabel_->setText("Failed to load processed data");
        }
    } else {
        QMessageBox::critical(this, "Processing Error",
                             QString("Failed to process ARINC file:\n%1").arg(processManager_->getLastError()));
        statusLabel_->setText("Failed to process ARINC file");
    }
}

void MainWindow::showProcessedFileBrowser() {
    QStringList jsonFiles = processManager_->getAvailableJsonFiles();

    if (jsonFiles.isEmpty()) {
        QMessageBox::information(this, "No Processed Files",
                               QString("No processed JSON files found in:\n%1\n\n"
                                      "Process an ARINC file first using 'Open ARINC File (.pc)...'")
                               .arg(processManager_->getOutputDirectory()));
        return;
    }

    // Create a simple selection dialog
    QDialog dialog(this);
    dialog.setWindowTitle("Browse Processed Files");
    dialog.setModal(true);
    dialog.resize(600, 400);

    auto layout = new QVBoxLayout(&dialog);

    auto label = new QLabel("Select a processed JSON file to load:");
    layout->addWidget(label);

    auto listWidget = new QListWidget();
    for (const QString& filePath : jsonFiles) {
        QFileInfo fileInfo(filePath);
        auto item = new QListWidgetItem(QString("%1 (%2)")
                                       .arg(fileInfo.fileName())
                                       .arg(fileInfo.lastModified().toString("yyyy-MM-dd hh:mm:ss")));
        item->setData(Qt::UserRole, filePath);
        listWidget->addItem(item);
    }
    layout->addWidget(listWidget);

    auto buttonLayout = new QHBoxLayout();
    auto loadButton = new QPushButton("Load Selected");
    auto cancelButton = new QPushButton("Cancel");
    auto deleteButton = new QPushButton("Delete Selected");

    buttonLayout->addWidget(deleteButton);
    buttonLayout->addStretch();
    buttonLayout->addWidget(loadButton);
    buttonLayout->addWidget(cancelButton);
    layout->addLayout(buttonLayout);

    // Connect buttons
    connect(loadButton, &QPushButton::clicked, [&]() {
        auto currentItem = listWidget->currentItem();
        if (currentItem) {
            QString filePath = currentItem->data(Qt::UserRole).toString();
            if (loadNavigationData(filePath)) {
                statusLabel_->setText("Data loaded successfully");
                updateFilterOptions();
                updateStatusInfo();
                mapWidget_->zoomToFit();
                dialog.accept();
            } else {
                QMessageBox::warning(&dialog, "Error", "Failed to load the selected file.");
            }
        }
    });

    connect(cancelButton, &QPushButton::clicked, &dialog, &QDialog::reject);

    connect(deleteButton, &QPushButton::clicked, [&]() {
        auto currentItem = listWidget->currentItem();
        if (currentItem) {
            QString filePath = currentItem->data(Qt::UserRole).toString();
            QFileInfo fileInfo(filePath);

            int ret = QMessageBox::question(&dialog, "Delete File",
                                          QString("Are you sure you want to delete:\n%1").arg(fileInfo.fileName()),
                                          QMessageBox::Yes | QMessageBox::No);

            if (ret == QMessageBox::Yes) {
                if (QFile::remove(filePath)) {
                    delete currentItem;
                    QMessageBox::information(&dialog, "Success", "File deleted successfully.");
                } else {
                    QMessageBox::warning(&dialog, "Error", "Failed to delete the file.");
                }
            }
        }
    });

    // Enable load button only when an item is selected
    loadButton->setEnabled(false);
    deleteButton->setEnabled(false);
    connect(listWidget, &QListWidget::currentItemChanged, [=](QListWidgetItem* current) {
        loadButton->setEnabled(current != nullptr);
        deleteButton->setEnabled(current != nullptr);
    });

    dialog.exec();
}

} // namespace arinc424

#include "ProcessManager.h"
#include <QApplication>
#include <QDir>
#include <QFileInfo>
#include <QStandardPaths>
#include <QProgressDialog>
#include <QTimer>
#include <QMessageBox>
#include <QDebug>
#include <QDateTime>

namespace arinc424 {

ProcessManager::ProcessManager(QObject* parent)
    : QObject(parent)
    , process_(std::make_unique<QProcess>(this))
    , progressDialog_(nullptr)
    , progressTimer_(new QTimer(this))
{
    // Setup process connections
    connect(process_.get(), QOverload<int, QProcess::ExitStatus>::of(&QProcess::finished),
            this, &ProcessManager::onProcessFinished);
    connect(process_.get(), &QProcess::errorOccurred,
            this, &ProcessManager::onProcessError);
    connect(process_.get(), &QProcess::readyReadStandardOutput,
            this, &ProcessManager::onProcessOutput);

    // Setup progress timer
    progressTimer_->setSingleShot(false);
    progressTimer_->setInterval(PROGRESS_UPDATE_MS);
    connect(progressTimer_, &QTimer::timeout, this, &ProcessManager::onProgressTimeout);

    // Ensure output directory exists
    ensureOutputDirectoryExists();
}

QString ProcessManager::processArincFile(const QString& arincFilePath, QWidget* parentWidget) {
    // Validate inputs
    if (arincFilePath.isEmpty()) {
        lastError_ = "Invalid file path provided";
        return QString();
    }

    if (!QFileInfo::exists(arincFilePath)) {
        lastError_ = QString("ARINC file does not exist: %1").arg(arincFilePath);
        return QString();
    }

    if (!isPythonScriptAvailable()) {
        lastError_ = "Python script not available or Python environment not properly configured";
        return QString();
    }

    // Ensure output directory exists
    if (!ensureOutputDirectoryExists()) {
        lastError_ = "Failed to create output directory";
        return QString();
    }

    // Generate output file path
    QString outputJsonPath = generateOutputFileName(arincFilePath);

    // Setup progress dialog
    setupProgressDialog(parentWidget, arincFilePath);

    // Prepare command
    QString pythonExe = findPythonExecutable();
    QString scriptPath = getPythonScriptPath();

    QStringList arguments;
    arguments << scriptPath
              << "--file" << arincFilePath
              << "--output" << outputJsonPath
              << "--quiet"; // Suppress verbose output

    currentOutputPath_ = outputJsonPath;
    
    // Start process
    qDebug() << "Starting process:" << pythonExe << arguments.join(" ");
    
    process_->start(pythonExe, arguments);
    
    if (!process_->waitForStarted(5000)) {
        lastError_ = QString("Failed to start Python process: %1").arg(process_->errorString());
        cleanupProcess();
        return QString();
    }
    
    emit processStarted();
    progressTimer_->start();
    
    // Wait for process to complete
    bool finished = process_->waitForFinished(PROCESS_TIMEOUT_MS);
    
    progressTimer_->stop();
    
    if (!finished) {
        lastError_ = "Process timed out";
        process_->kill();
        cleanupProcess();
        return QString();
    }
    
    // Check if output file was created
    if (!QFileInfo::exists(outputJsonPath)) {
        lastError_ = "Output JSON file was not created";
        cleanupProcess();
        return QString();
    }

    cleanupProcess();
    return outputJsonPath;
}

bool ProcessManager::isPythonScriptAvailable() const {
    return !findPythonExecutable().isEmpty() && !findPythonScript().isEmpty() && validatePythonEnvironment();
}

QString ProcessManager::getPythonScriptPath() const {
    return findPythonScript();
}

QString ProcessManager::getOutputDirectory() const {
    // Create output directory relative to application directory
    QDir appDir(QApplication::applicationDirPath());

    // If we're in a build directory, go up one level
    if (appDir.dirName() == "build") {
        appDir.cdUp();
    }

    return appDir.absoluteFilePath(OUTPUT_SUBDIR);
}

QStringList ProcessManager::getAvailableJsonFiles() const {
    QDir outputDir(getOutputDirectory());
    if (!outputDir.exists()) {
        return QStringList();
    }

    // Get all JSON files in the output directory
    QStringList nameFilters;
    nameFilters << "*.json";

    QFileInfoList fileInfos = outputDir.entryInfoList(nameFilters, QDir::Files, QDir::Time | QDir::Reversed);

    QStringList filePaths;
    for (const QFileInfo& fileInfo : fileInfos) {
        filePaths.append(fileInfo.absoluteFilePath());
    }

    return filePaths;
}

bool ProcessManager::ensureOutputDirectoryExists() {
    QDir outputDir(getOutputDirectory());
    if (!outputDir.exists()) {
        QDir().mkpath(outputDir.absolutePath());
    }
    return outputDir.exists();
}

QString ProcessManager::generateOutputFileName(const QString& arincFilePath) const {
    QFileInfo fileInfo(arincFilePath);
    QString baseName = fileInfo.baseName();

    // Add timestamp to make files unique
    QString timestamp = QDateTime::currentDateTime().toString("yyyyMMdd_hhmmss");
    QString fileName = QString("%1_%2.json").arg(baseName).arg(timestamp);

    return QDir(getOutputDirectory()).absoluteFilePath(fileName);
}

QString ProcessManager::findPythonExecutable() const {
    // Try different Python executable names
    QStringList pythonNames = {"python3", "python"};
    
    for (const QString& name : pythonNames) {
        QString path = QStandardPaths::findExecutable(name);
        if (!path.isEmpty()) {
            return path;
        }
    }
    
    return QString();
}

QString ProcessManager::findPythonScript() const {
    // Look for the script in the application directory and parent directories
    QStringList searchPaths;
    
    // Application directory
    searchPaths << QApplication::applicationDirPath();
    
    // Parent directory (for build directory structure)
    QDir appDir(QApplication::applicationDirPath());
    if (appDir.cdUp()) {
        searchPaths << appDir.absolutePath();
    }
    
    // Current working directory
    searchPaths << QDir::currentPath();
    
    for (const QString& path : searchPaths) {
        QString scriptPath = QDir(path).absoluteFilePath("arinc424_to_json.py");
        if (QFileInfo::exists(scriptPath)) {
            return scriptPath;
        }
    }
    
    return QString();
}

bool ProcessManager::validatePythonEnvironment() const {
    QString pythonExe = findPythonExecutable();
    if (pythonExe.isEmpty()) {
        return false;
    }
    
    // Quick test to see if Python can import the arinc424 module
    QProcess testProcess;
    testProcess.start(pythonExe, QStringList() << "-c" << "import arinc424; print('OK')");
    
    if (!testProcess.waitForStarted(5000) || !testProcess.waitForFinished(10000)) {
        return false;
    }
    
    QString output = testProcess.readAllStandardOutput().trimmed();
    return output == "OK";
}

void ProcessManager::setupProgressDialog(QWidget* parent, const QString& arincFilePath) {
    if (progressDialog_) {
        delete progressDialog_;
    }
    
    progressDialog_ = new QProgressDialog(parent);
    progressDialog_->setWindowTitle("Processing ARINC 424 File");
    progressDialog_->setLabelText(QString("Processing: %1\n\nThis may take several minutes for large files...")
                                 .arg(QFileInfo(arincFilePath).fileName()));
    progressDialog_->setRange(0, 0); // Indeterminate progress
    progressDialog_->setModal(true);
    progressDialog_->setCancelButton(nullptr); // No cancel for now
    progressDialog_->show();
    
    // Process events to show the dialog
    QApplication::processEvents();
}

void ProcessManager::cleanupProcess() {
    if (progressDialog_) {
        progressDialog_->hide();
        progressDialog_->deleteLater();
        progressDialog_ = nullptr;
    }
    
    progressTimer_->stop();
}

void ProcessManager::onProcessFinished(int exitCode, QProcess::ExitStatus exitStatus) {
    progressTimer_->stop();
    
    bool success = (exitStatus == QProcess::NormalExit && exitCode == 0);
    
    if (!success) {
        QString errorOutput = process_->readAllStandardError();
        lastError_ = QString("Process failed with exit code %1: %2").arg(exitCode).arg(errorOutput);
    }
    
    emit processFinished(success);
}

void ProcessManager::onProcessError(QProcess::ProcessError error) {
    progressTimer_->stop();
    
    QString errorString;
    switch (error) {
        case QProcess::FailedToStart:
            errorString = "Failed to start process";
            break;
        case QProcess::Crashed:
            errorString = "Process crashed";
            break;
        case QProcess::Timedout:
            errorString = "Process timed out";
            break;
        case QProcess::WriteError:
            errorString = "Write error";
            break;
        case QProcess::ReadError:
            errorString = "Read error";
            break;
        default:
            errorString = "Unknown error";
            break;
    }
    
    lastError_ = errorString;
    emit processError(errorString);
}

void ProcessManager::onProcessOutput() {
    QString output = process_->readAllStandardOutput();
    emit processProgress(output);
}

void ProcessManager::onProgressTimeout() {
    if (progressDialog_) {
        // Update progress dialog to show it's still working
        static int dots = 0;
        QString dotString = QString(".").repeated((dots % 3) + 1);
        progressDialog_->setLabelText(QString("Processing: %1%2\n\nThis may take several minutes for large files...")
                                     .arg(QFileInfo(currentOutputPath_).baseName())
                                     .arg(dotString));
        dots++;
        
        QApplication::processEvents();
    }
}

} // namespace arinc424

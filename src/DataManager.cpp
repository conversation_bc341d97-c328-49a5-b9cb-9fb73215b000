#include "DataManager.h"
#include "models/NavigationData.h"

namespace arinc424 {

DataManager::DataManager(QObject* parent)
    : QObject(parent)
    , navData_(std::make_shared<NavigationData>())
{
}

bool DataManager::loadFromJsonFile(const QString& filePath) {
    if (!navData_) {
        navData_ = std::make_shared<NavigationData>();
    }
    
    bool success = navData_->loadFromJson(filePath.toStdString());
    
    if (success) {
        lastError_.clear();
        emit dataLoaded();
    } else {
        setError("Failed to load data from file: " + filePath);
    }
    
    return success;
}

bool DataManager::loadFromJsonString(const QString& jsonString) {
    if (!navData_) {
        navData_ = std::make_shared<NavigationData>();
    }
    
    bool success = navData_->loadFromJsonString(jsonString.toStdString());
    
    if (success) {
        lastError_.clear();
        emit dataLoaded();
    } else {
        setError("Failed to parse JSON data");
    }
    
    return success;
}

bool DataManager::hasData() const {
    return navData_ && (navData_->getAirportCount() > 0 || 
                       navData_->getWaypointCount() > 0 || 
                       navData_->getSIDSTARCount() > 0);
}

int DataManager::getAirportCount() const {
    return navData_ ? static_cast<int>(navData_->getAirportCount()) : 0;
}

int DataManager::getWaypointCount() const {
    return navData_ ? static_cast<int>(navData_->getWaypointCount()) : 0;
}

int DataManager::getProcedureCount() const {
    return navData_ ? static_cast<int>(navData_->getSIDSTARCount()) : 0;
}

void DataManager::clearData() {
    if (navData_) {
        navData_->clear();
    }
    lastError_.clear();
    emit dataCleared();
}

void DataManager::setError(const QString& error) {
    lastError_ = error;
    emit loadingError(error);
}

} // namespace arinc424

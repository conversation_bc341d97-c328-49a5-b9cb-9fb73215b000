#include "FilterManager.h"
#include "models/NavigationData.h"
#include "models/Airport.h"
#include "models/Waypoint.h"
#include "models/SIDSTARApproach.h"
#include <algorithm>
#include <set>

namespace arinc424 {

FilterManager::FilterManager(QObject* parent)
    : QObject(parent)
{
}

void FilterManager::setNavigationData(std::shared_ptr<NavigationData> navData) {
    navData_ = navData;
    invalidateResults();
    
    emit availableRegionsChanged();
    emit availableICAOCodesChanged();
    emit availableRecordTypesChanged();
}

QStringList FilterManager::getAvailableRegions() const {
    if (!navData_) {
        return QStringList();
    }
    
    auto regions = navData_->getRegions();
    QStringList result;
    for (const auto& region : regions) {
        result.append(QString::fromStdString(region));
    }
    result.sort();
    return result;
}

QStringList FilterManager::getAvailableICAOCodes(const QString& region) const {
    if (!navData_) {
        return QStringList();
    }
    
    std::string regionStr = region.isEmpty() ? "" : region.toStdString();
    auto icaoCodes = navData_->getICAOCodes(regionStr);
    
    QStringList result;
    for (const auto& icao : icaoCodes) {
        result.append(QString::fromStdString(icao));
    }
    result.sort();
    return result;
}

QStringList FilterManager::getAvailableRecordTypes(const QString& icaoCode) const {
    if (!navData_) {
        return QStringList();
    }
    
    std::string icaoStr = icaoCode.isEmpty() ? "" : icaoCode.toStdString();
    auto recordTypes = navData_->getRecordTypes(icaoStr);
    
    QStringList result;
    for (const auto& type : recordTypes) {
        result.append(QString::fromStdString(type));
    }
    result.sort();
    return result;
}

void FilterManager::applyFilters(const QString& region, const QString& icaoCode, const QStringList& recordTypes) {
    currentRegion_ = region;
    currentIcaoCode_ = icaoCode;
    currentRecordTypes_ = recordTypes;
    
    invalidateResults();
    emit filtersApplied();
}

void FilterManager::clearFilters() {
    currentRegion_.clear();
    currentIcaoCode_.clear();
    currentRecordTypes_.clear();
    
    invalidateResults();
    emit filtersApplied();
}

std::vector<std::shared_ptr<Airport>> FilterManager::getFilteredAirports() const {
    updateFilteredResults();
    return filteredAirports_;
}

std::vector<std::shared_ptr<Waypoint>> FilterManager::getFilteredWaypoints() const {
    updateFilteredResults();
    return filteredWaypoints_;
}

std::vector<std::shared_ptr<SIDSTARApproach>> FilterManager::getFilteredProcedures() const {
    updateFilteredResults();
    return filteredProcedures_;
}

std::vector<std::shared_ptr<Waypoint>> FilterManager::getAllRelatedWaypoints() const {
    updateFilteredResults();
    
    // Start with directly filtered waypoints
    auto result = filteredWaypoints_;
    
    // Add waypoints referenced by filtered procedures
    auto referencedWaypoints = getReferencedWaypoints(filteredProcedures_);
    
    // Merge and remove duplicates
    std::set<std::string> seenIdentifiers;
    std::vector<std::shared_ptr<Waypoint>> allWaypoints;
    
    // Add filtered waypoints
    for (const auto& wp : result) {
        if (seenIdentifiers.find(wp->getIdentifier()) == seenIdentifiers.end()) {
            allWaypoints.push_back(wp);
            seenIdentifiers.insert(wp->getIdentifier());
        }
    }
    
    // Add referenced waypoints
    for (const auto& wp : referencedWaypoints) {
        if (seenIdentifiers.find(wp->getIdentifier()) == seenIdentifiers.end()) {
            allWaypoints.push_back(wp);
            seenIdentifiers.insert(wp->getIdentifier());
        }
    }
    
    return allWaypoints;
}

int FilterManager::getFilteredAirportCount() const {
    return static_cast<int>(getFilteredAirports().size());
}

int FilterManager::getFilteredWaypointCount() const {
    return static_cast<int>(getFilteredWaypoints().size());
}

int FilterManager::getFilteredProcedureCount() const {
    return static_cast<int>(getFilteredProcedures().size());
}

void FilterManager::onDataChanged() {
    invalidateResults();
    emit availableRegionsChanged();
    emit availableICAOCodesChanged();
    emit availableRecordTypesChanged();
}

void FilterManager::invalidateResults() {
    resultsValid_ = false;
}

void FilterManager::updateFilteredResults() const {
    if (resultsValid_ || !navData_) {
        return;
    }
    
    filteredAirports_.clear();
    filteredWaypoints_.clear();
    filteredProcedures_.clear();
    
    // Filter airports
    if (currentRecordTypes_.isEmpty() || currentRecordTypes_.contains("Airport")) {
        for (const auto& airport : navData_->getAirports()) {
            if (matchesFilter(airport)) {
                filteredAirports_.push_back(airport);
            }
        }
    }
    
    // Filter waypoints
    if (currentRecordTypes_.isEmpty() || currentRecordTypes_.contains("Waypoint")) {
        for (const auto& waypoint : navData_->getWaypoints()) {
            if (matchesFilter(waypoint)) {
                filteredWaypoints_.push_back(waypoint);
            }
        }
    }
    
    // Filter procedures
    if (currentRecordTypes_.isEmpty() || 
        currentRecordTypes_.contains("SID") || 
        currentRecordTypes_.contains("STAR") || 
        currentRecordTypes_.contains("Approach")) {
        
        for (const auto& procedure : navData_->getSIDSTARApproaches()) {
            if (matchesFilter(procedure)) {
                filteredProcedures_.push_back(procedure);
            }
        }
    }
    
    resultsValid_ = true;
}

bool FilterManager::matchesFilter(std::shared_ptr<Airport> airport) const {
    // Check region filter
    if (!currentRegion_.isEmpty() &&
        QString::fromStdString(airport->getRegion()) != currentRegion_) {
        return false;
    }

    // Check ICAO filter
    if (!currentIcaoCode_.isEmpty() &&
        QString::fromStdString(airport->getIcaoCode()) != currentIcaoCode_) {
        return false;
    }

    return true;
}

bool FilterManager::matchesFilter(std::shared_ptr<Waypoint> waypoint) const {
    // Check region filter
    if (!currentRegion_.isEmpty() &&
        QString::fromStdString(waypoint->getRegion()) != currentRegion_) {
        return false;
    }

    // Check ICAO filter (waypoints might be associated with airport regions)
    if (!currentIcaoCode_.isEmpty()) {
        // Check if waypoint's region or ICAO code matches
        if (QString::fromStdString(waypoint->getRegion()) != currentIcaoCode_ &&
            QString::fromStdString(waypoint->getIcaoCode()) != currentIcaoCode_) {
            return false;
        }
    }

    return true;
}

bool FilterManager::matchesFilter(std::shared_ptr<SIDSTARApproach> procedure) const {
    // Check ICAO filter (procedures are always associated with an airport)
    if (!currentIcaoCode_.isEmpty() &&
        QString::fromStdString(procedure->getAirportIdentifier()) != currentIcaoCode_) {
        return false;
    }

    // Check record type filter
    if (!currentRecordTypes_.isEmpty()) {
        QString procType = QString::fromStdString(procedure->getProcedureType());
        if (!currentRecordTypes_.contains(procType)) {
            return false;
        }
    }

    return true;
}

std::vector<std::shared_ptr<Waypoint>> FilterManager::getReferencedWaypoints(
    const std::vector<std::shared_ptr<SIDSTARApproach>>& procedures) const {

    std::vector<std::shared_ptr<Waypoint>> referencedWaypoints;
    std::set<std::string> seenIdentifiers;

    for (const auto& procedure : procedures) {
        // Get the fix identifier from the procedure
        std::string fixId = procedure->getFixIdentifier();
        if (fixId.empty() || fixId == "     ") {
            continue;
        }

        // Trim whitespace
        fixId.erase(fixId.find_last_not_of(" \t\n\r\f\v") + 1);

        if (seenIdentifiers.find(fixId) != seenIdentifiers.end()) {
            continue; // Already processed this waypoint
        }

        // Find the waypoint
        auto waypoint = navData_->findWaypoint(fixId);
        if (waypoint) {
            referencedWaypoints.push_back(waypoint);
            seenIdentifiers.insert(fixId);
        }
    }

    return referencedWaypoints;
}

} // namespace arinc424

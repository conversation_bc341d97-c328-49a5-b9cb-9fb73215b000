#include "models/NavigationData.h"
#include "models/Airport.h"
#include "models/Waypoint.h"
#include "models/SIDSTARApproach.h"
#include <fstream>
#include <iostream>
#include <set>

namespace arinc424 {

bool NavigationData::loadFromJson(const std::string& jsonFilePath) {
    std::ifstream file(jsonFilePath);
    if (!file.is_open()) {
        std::cerr << "Error: Could not open file " << jsonFilePath << std::endl;
        return false;
    }
    
    nlohmann::json json;
    try {
        file >> json;
    } catch (const std::exception& e) {
        std::cerr << "Error parsing JSON: " << e.what() << std::endl;
        return false;
    }
    
    return loadFromJsonString(json.dump());
}

bool NavigationData::loadFromJsonString(const std::string& jsonString) {
    try {
        nlohmann::json json = nlohmann::json::parse(jsonString);
        
        // Clear existing data
        clear();
        
        // Parse different record types
        parseAirports(json);
        parseWaypoints(json);
        parseSIDSTARApproaches(json);
        
        // Build lookup maps for fast access
        buildLookupMaps();
        
        std::cout << "Loaded " << airports_.size() << " airports, " 
                  << waypoints_.size() << " waypoints, " 
                  << sidStarApproaches_.size() << " SID/STAR/Approach procedures" << std::endl;
        
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "Error loading navigation data: " << e.what() << std::endl;
        return false;
    }
}

void NavigationData::parseAirports(const nlohmann::json& json) {
    if (json.contains("airport") && json["airport"].is_array()) {
        for (const auto& airportJson : json["airport"]) {
            auto airport = Airport::fromJson(airportJson);
            if (airport) {
                airports_.push_back(airport);
            }
        }
    }
}

void NavigationData::parseWaypoints(const nlohmann::json& json) {
    // Parse enroute waypoints
    if (json.contains("enroute_waypoint") && json["enroute_waypoint"].is_array()) {
        for (const auto& waypointJson : json["enroute_waypoint"]) {
            auto waypoint = Waypoint::fromJson(waypointJson);
            if (waypoint) {
                waypoints_.push_back(waypoint);
            }
        }
    }
    
    // Parse terminal waypoints
    if (json.contains("terminal_waypoint") && json["terminal_waypoint"].is_array()) {
        for (const auto& waypointJson : json["terminal_waypoint"]) {
            auto waypoint = Waypoint::fromJson(waypointJson);
            if (waypoint) {
                waypoints_.push_back(waypoint);
            }
        }
    }
}

void NavigationData::parseSIDSTARApproaches(const nlohmann::json& json) {
    // Parse SIDs
    if (json.contains("sids") && json["sids"].is_array()) {
        for (const auto& sidJson : json["sids"]) {
            auto sid = SIDSTARApproach::fromJson(sidJson);
            if (sid) {
                sidStarApproaches_.push_back(sid);
            }
        }
    }
    
    // Parse STARs
    if (json.contains("stars") && json["stars"].is_array()) {
        for (const auto& starJson : json["stars"]) {
            auto star = SIDSTARApproach::fromJson(starJson);
            if (star) {
                sidStarApproaches_.push_back(star);
            }
        }
    }
    
    // Parse Instrument Approaches
    if (json.contains("instrument_approaches") && json["instrument_approaches"].is_array()) {
        for (const auto& approachJson : json["instrument_approaches"]) {
            auto approach = SIDSTARApproach::fromJson(approachJson);
            if (approach) {
                sidStarApproaches_.push_back(approach);
            }
        }
    }
}

void NavigationData::buildLookupMaps() {
    // Build airport lookup map
    airportLookup_.clear();
    for (const auto& airport : airports_) {
        airportLookup_[airport->getIcaoCode()] = airport;
    }

    // Build waypoint lookup map
    waypointLookup_.clear();
    for (const auto& waypoint : waypoints_) {
        waypointLookup_[waypoint->getIdentifier()].push_back(waypoint);
    }

    // Build SID/STAR lookup map
    sidStarLookup_.clear();
    for (const auto& procedure : sidStarApproaches_) {
        sidStarLookup_[procedure->getAirportIdentifier()].push_back(procedure);
    }
}

std::shared_ptr<Airport> NavigationData::findAirport(const std::string& icaoCode) const {
    auto it = airportLookup_.find(icaoCode);
    return (it != airportLookup_.end()) ? it->second : nullptr;
}

std::shared_ptr<Waypoint> NavigationData::findWaypoint(const std::string& identifier, const std::string& region) const {
    auto it = waypointLookup_.find(identifier);
    if (it == waypointLookup_.end()) {
        return nullptr;
    }

    // If region is specified, try to find exact match
    if (!region.empty()) {
        for (const auto& waypoint : it->second) {
            if (waypoint->getRegion() == region) {
                return waypoint;
            }
        }
    }

    // Return first match if no region specified or no exact region match
    return it->second.empty() ? nullptr : it->second[0];
}

std::vector<std::shared_ptr<SIDSTARApproach>> NavigationData::findSIDSTARByAirport(const std::string& airportIcao) const {
    auto it = sidStarLookup_.find(airportIcao);
    return (it != sidStarLookup_.end()) ? it->second : std::vector<std::shared_ptr<SIDSTARApproach>>();
}

std::vector<std::string> NavigationData::getRegions() const {
    std::set<std::string> regions;

    for (const auto& airport : airports_) {
        if (!airport->getRegion().empty()) {
            regions.insert(airport->getRegion());
        }
    }

    for (const auto& waypoint : waypoints_) {
        if (!waypoint->getRegion().empty()) {
            regions.insert(waypoint->getRegion());
        }
    }

    return std::vector<std::string>(regions.begin(), regions.end());
}

std::vector<std::string> NavigationData::getICAOCodes(const std::string& region) const {
    std::set<std::string> icaoCodes;

    for (const auto& airport : airports_) {
        if (region.empty() || airport->getRegion() == region) {
            icaoCodes.insert(airport->getIcaoCode());
        }
    }

    return std::vector<std::string>(icaoCodes.begin(), icaoCodes.end());
}

std::vector<std::string> NavigationData::getRecordTypes(const std::string& icaoCode) const {
    std::set<std::string> recordTypes;

    if (icaoCode.empty()) {
        // Return all record types
        recordTypes.insert("Airport");
        recordTypes.insert("Waypoint");
        recordTypes.insert("SID");
        recordTypes.insert("STAR");
        recordTypes.insert("Approach");
    } else {
        // Check if airport exists
        if (findAirport(icaoCode)) {
            recordTypes.insert("Airport");
        }

        // Check for procedures
        auto procedures = findSIDSTARByAirport(icaoCode);
        for (const auto& proc : procedures) {
            recordTypes.insert(proc->getProcedureType());
        }

        // Check for waypoints (terminal waypoints might be associated with airport region)
        for (const auto& waypoint : waypoints_) {
            if (waypoint->getRegion() == icaoCode || waypoint->getIcaoCode() == icaoCode) {
                recordTypes.insert("Waypoint");
                break;
            }
        }
    }

    return std::vector<std::string>(recordTypes.begin(), recordTypes.end());
}

void NavigationData::clear() {
    airports_.clear();
    waypoints_.clear();
    sidStarApproaches_.clear();
    airportLookup_.clear();
    waypointLookup_.clear();
    sidStarLookup_.clear();
}

} // namespace arinc424

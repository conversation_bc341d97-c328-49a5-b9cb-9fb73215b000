#include "models/Airport.h"
#include <sstream>
#include <algorithm>
#include <cctype>

namespace arinc424 {

std::shared_ptr<Airport> Airport::fromJson(const nlohmann::json& json) {
    auto airport = std::make_shared<Airport>();
    
    try {
        // Extract ICAO code
        if (json.contains("Airport ICAO Identifier")) {
            airport->setIcaoCode(trim(json["Airport ICAO Identifier"].get<std::string>()));
        }
        
        // Extract name
        if (json.contains("Airport Name")) {
            airport->setName(trim(json["Airport Name"].get<std::string>()));
        }
        
        // Extract IATA code
        if (json.contains("ATA/IATA Designator")) {
            airport->setIataCode(trim(json["ATA/IATA Designator"].get<std::string>()));
        }
        
        // Extract region from Customer/Area Code
        if (json.contains("Customer / Area Code")) {
            airport->setRegion(trim(json["Customer / Area Code"].get<std::string>()));
        }
        
        // Extract coordinates
        if (json.contains("Airport Reference Pt. Latitude")) {
            airport->setLatitude(parseCoordinate(json["Airport Reference Pt. Latitude"].get<std::string>()));
        }
        if (json.contains("Airport Reference Pt. Longitude")) {
            airport->setLongitude(parseCoordinate(json["Airport Reference Pt. Longitude"].get<std::string>()));
        }
        
        // Extract elevation
        if (json.contains("Airport Elevation")) {
            std::string elevStr = json["Airport Elevation"].get<std::string>();
            if (!elevStr.empty() && elevStr != "     ") {
                airport->setElevation(std::stoi(elevStr));
            }
        }
        
        // Extract record type and section code
        if (json.contains("Record Type")) {
            airport->setRecordType(trim(json["Record Type"].get<std::string>()));
        }
        if (json.contains("Section Code")) {
            airport->setSectionCode(trim(json["Section Code"].get<std::string>()));
        }
        
        // Extract metadata
        if (json.contains("_source_file")) {
            airport->setSourceFile(json["_source_file"].get<std::string>());
        }
        if (json.contains("_line_number")) {
            airport->setLineNumber(json["_line_number"].get<int>());
        }
        
    } catch (const std::exception& e) {
        // Return null pointer if parsing fails
        return nullptr;
    }
    
    return airport->isValid() ? airport : nullptr;
}

double Airport::parseCoordinate(const std::string& coord) {
    if (coord.empty() || coord.length() < 9) {
        return 0.0;
    }
    
    // ARINC 424 coordinate format: NDDMMSSSS or WDDDMMSSSS
    char hemisphere = coord[0];
    std::string numPart = coord.substr(1);
    
    double degrees, minutes, seconds;
    
    if (hemisphere == 'N' || hemisphere == 'S') {
        // Latitude: DDMMSSSS
        degrees = std::stod(numPart.substr(0, 2));
        minutes = std::stod(numPart.substr(2, 2));
        seconds = std::stod(numPart.substr(4, 4)) / 100.0;
    } else {
        // Longitude: DDDMMSSSS
        degrees = std::stod(numPart.substr(0, 3));
        minutes = std::stod(numPart.substr(3, 2));
        seconds = std::stod(numPart.substr(5, 4)) / 100.0;
    }
    
    double result = degrees + minutes / 60.0 + seconds / 3600.0;
    
    if (hemisphere == 'S' || hemisphere == 'W') {
        result = -result;
    }
    
    return result;
}

std::string Airport::trim(const std::string& str) {
    auto start = str.begin();
    while (start != str.end() && std::isspace(*start)) {
        start++;
    }
    
    auto end = str.end();
    do {
        end--;
    } while (std::distance(start, end) > 0 && std::isspace(*end));
    
    return std::string(start, end + 1);
}

bool Airport::isValid() const {
    return !icaoCode_.empty() && latitude_ != 0.0 && longitude_ != 0.0;
}

std::string Airport::toString() const {
    std::ostringstream oss;
    oss << "Airport{ICAO: " << icaoCode_ 
        << ", Name: " << name_
        << ", IATA: " << iataCode_
        << ", Lat: " << latitude_
        << ", Lon: " << longitude_
        << ", Elev: " << elevation_ << "ft}";
    return oss.str();
}

} // namespace arinc424

#include "models/SIDSTARApproach.h"
#include "models/NavigationData.h"
#include "models/Waypoint.h"
#include <sstream>
#include <algorithm>
#include <cctype>

namespace arinc424 {

std::shared_ptr<SIDSTARApproach> SIDSTARApproach::fromJson(const nlohmann::json& json) {
    auto procedure = std::make_shared<SIDSTARApproach>();
    
    try {
        // Extract airport identifier
        if (json.contains("Airport Identifier")) {
            procedure->setAirportIdentifier(trim(json["Airport Identifier"].get<std::string>()));
        }
        
        // Extract procedure identifier
        if (json.contains("SID/STAR/Approach Identifier")) {
            procedure->setIdentifier(trim(json["SID/STAR/Approach Identifier"].get<std::string>()));
        }
        
        // Extract transition identifier
        if (json.contains("Transition Identifier")) {
            procedure->setTransitionIdentifier(trim(json["Transition Identifier"].get<std::string>()));
        }
        
        // Extract fix identifier
        if (json.contains("Fix Identifier")) {
            procedure->setFixIdentifier(trim(json["Fix Identifier"].get<std::string>()));
        }
        
        // Extract route type
        if (json.contains("Route Type")) {
            procedure->setRouteType(trim(json["Route Type"].get<std::string>()));
        }
        
        // Extract sequence number
        if (json.contains("Sequence Number")) {
            procedure->setSequenceNumber(parseSequenceNumber(json["Sequence Number"].get<std::string>()));
        }
        
        // Extract record type and section code
        if (json.contains("Record Type")) {
            procedure->setRecordType(trim(json["Record Type"].get<std::string>()));
        }
        if (json.contains("Section Code")) {
            procedure->setSectionCode(trim(json["Section Code"].get<std::string>()));
        }
        
        // Extract ICAO code
        if (json.contains("ICAO Code")) {
            procedure->setIcaoCode(trim(json["ICAO Code"].get<std::string>()));
        }
        
        // Extract leg distance
        if (json.contains("Leg Distance")) {
            procedure->setLegDistance(trim(json["Leg Distance"].get<std::string>()));
        }
        
        // Extract metadata
        if (json.contains("_source_file")) {
            procedure->setSourceFile(json["_source_file"].get<std::string>());
        }
        if (json.contains("_line_number")) {
            procedure->setLineNumber(json["_line_number"].get<int>());
        }
        
    } catch (const std::exception& e) {
        // Return null pointer if parsing fails
        return nullptr;
    }
    
    return procedure->isValid() ? procedure : nullptr;
}

std::string SIDSTARApproach::trim(const std::string& str) {
    auto start = str.begin();
    while (start != str.end() && std::isspace(*start)) {
        start++;
    }
    
    auto end = str.end();
    do {
        end--;
    } while (std::distance(start, end) > 0 && std::isspace(*end));
    
    return std::string(start, end + 1);
}

int SIDSTARApproach::parseSequenceNumber(const std::string& seq) {
    try {
        return std::stoi(trim(seq));
    } catch (const std::exception&) {
        return 0;
    }
}

bool SIDSTARApproach::isValid() const {
    return !airportIdentifier_.empty() && !identifier_.empty() && !fixIdentifier_.empty();
}

std::string SIDSTARApproach::toString() const {
    std::ostringstream oss;
    oss << "Procedure{Airport: " << airportIdentifier_ 
        << ", ID: " << identifier_
        << ", Transition: " << transitionIdentifier_
        << ", Fix: " << fixIdentifier_
        << ", Seq: " << sequenceNumber_
        << ", Type: " << getProcedureType() << "}";
    return oss.str();
}

std::string SIDSTARApproach::getProcedureType() const {
    if (sectionCode_ == "PD") {
        return "SID";
    } else if (sectionCode_ == "PE") {
        return "STAR";
    } else if (sectionCode_ == "PF") {
        return "APPROACH";
    }
    return "UNKNOWN";
}

std::string SIDSTARApproach::getFullIdentifier() const {
    if (transitionIdentifier_.empty() || transitionIdentifier_ == "     ") {
        return identifier_;
    }
    return identifier_ + "." + transitionIdentifier_;
}

std::vector<std::shared_ptr<Waypoint>> SIDSTARApproach::buildRoute(const NavigationData& navData) const {
    std::vector<std::shared_ptr<Waypoint>> route;
    
    // Find the waypoint for this fix
    auto waypoint = navData.findWaypoint(trim(fixIdentifier_));
    if (waypoint) {
        route.push_back(waypoint);
    }
    
    return route;
}

} // namespace arinc424

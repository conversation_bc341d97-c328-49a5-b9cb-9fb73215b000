#include "models/Waypoint.h"
#include <sstream>
#include <algorithm>
#include <cctype>

namespace arinc424 {

std::shared_ptr<Waypoint> Waypoint::fromJson(const nlohmann::json& json) {
    auto waypoint = std::make_shared<Waypoint>();
    
    try {
        // Extract identifier
        if (json.contains("Waypoint Identifier")) {
            waypoint->setIdentifier(trim(json["Waypoint Identifier"].get<std::string>()));
        }
        
        // Extract name/description
        if (json.contains("Waypoint Name / Desc")) {
            waypoint->setName(trim(json["Waypoint Name / Desc"].get<std::string>()));
        }
        
        // Extract region
        if (json.contains("Region Code")) {
            waypoint->setRegion(trim(json["Region Code"].get<std::string>()));
        }
        
        // Extract ICAO code
        if (json.contains("ICAO Code")) {
            waypoint->setIcaoCode(trim(json["ICAO Code"].get<std::string>()));
        }
        
        // Extract coordinates
        if (json.contains("Waypoint Latitude")) {
            waypoint->setLatitude(parseCoordinate(json["Waypoint Latitude"].get<std::string>()));
        }
        if (json.contains("Waypoint Longitude")) {
            waypoint->setLongitude(parseCoordinate(json["Waypoint Longitude"].get<std::string>()));
        }
        
        // Extract type and usage
        if (json.contains("Waypoint Type")) {
            waypoint->setType(trim(json["Waypoint Type"].get<std::string>()));
        }
        if (json.contains("Waypoint Usage")) {
            waypoint->setUsage(trim(json["Waypoint Usage"].get<std::string>()));
        }
        
        // Extract record type and section code
        if (json.contains("Record Type")) {
            waypoint->setRecordType(trim(json["Record Type"].get<std::string>()));
        }
        if (json.contains("Section Code")) {
            waypoint->setSectionCode(trim(json["Section Code"].get<std::string>()));
        }
        
        // Extract metadata
        if (json.contains("_source_file")) {
            waypoint->setSourceFile(json["_source_file"].get<std::string>());
        }
        if (json.contains("_line_number")) {
            waypoint->setLineNumber(json["_line_number"].get<int>());
        }
        
    } catch (const std::exception& e) {
        // Return null pointer if parsing fails
        return nullptr;
    }
    
    return waypoint->isValid() ? waypoint : nullptr;
}

double Waypoint::parseCoordinate(const std::string& coord) {
    if (coord.empty() || coord.length() < 9) {
        return 0.0;
    }
    
    // ARINC 424 coordinate format: NDDMMSSSS or WDDDMMSSSS
    char hemisphere = coord[0];
    std::string numPart = coord.substr(1);
    
    double degrees, minutes, seconds;
    
    if (hemisphere == 'N' || hemisphere == 'S') {
        // Latitude: DDMMSSSS
        degrees = std::stod(numPart.substr(0, 2));
        minutes = std::stod(numPart.substr(2, 2));
        seconds = std::stod(numPart.substr(4, 4)) / 100.0;
    } else {
        // Longitude: DDDMMSSSS
        degrees = std::stod(numPart.substr(0, 3));
        minutes = std::stod(numPart.substr(3, 2));
        seconds = std::stod(numPart.substr(5, 4)) / 100.0;
    }
    
    double result = degrees + minutes / 60.0 + seconds / 3600.0;
    
    if (hemisphere == 'S' || hemisphere == 'W') {
        result = -result;
    }
    
    return result;
}

std::string Waypoint::trim(const std::string& str) {
    auto start = str.begin();
    while (start != str.end() && std::isspace(*start)) {
        start++;
    }
    
    auto end = str.end();
    do {
        end--;
    } while (std::distance(start, end) > 0 && std::isspace(*end));
    
    return std::string(start, end + 1);
}

bool Waypoint::isValid() const {
    return !identifier_.empty() && latitude_ != 0.0 && longitude_ != 0.0;
}

std::string Waypoint::toString() const {
    std::ostringstream oss;
    oss << "Waypoint{ID: " << identifier_ 
        << ", Name: " << name_
        << ", Region: " << region_
        << ", Lat: " << latitude_
        << ", Lon: " << longitude_
        << ", Type: " << type_ << "}";
    return oss.str();
}

std::string Waypoint::getFullIdentifier() const {
    if (region_.empty()) {
        return identifier_;
    }
    return identifier_ + " (" + region_ + ")";
}

} // namespace arinc424

#include "MapWidget.h"
#include "FilterManager.h"
#include "models/NavigationData.h"
#include "models/Airport.h"
#include "models/Waypoint.h"
#include "models/SIDSTARApproach.h"
#include "utils/CoordinateConverter.h"
#include <QPainter>
#include <QMouseEvent>
#include <QWheelEvent>
#include <QTimer>
#include <QDebug>
#include <algorithm>
#include <cmath>
#include <map>

namespace arinc424 {

MapWidget::MapWidget(QWidget* parent)
    : QWidget(parent)
    , converter_(std::make_unique<utils::CoordinateConverter>())
    , updateTimer_(new QTimer(this))
{
    setMinimumSize(400, 300);
    setMouseTracking(true);
    
    // Setup update timer for smooth interaction
    updateTimer_->setSingleShot(true);
    updateTimer_->setInterval(16); // ~60 FPS
    connect(updateTimer_, &QTimer::timeout, this, &MapWidget::onUpdateTimer);
    
    setupCoordinateConverter();
}

MapWidget::~MapWidget() = default;

void MapWidget::setNavigationData(std::shared_ptr<NavigationData> navData) {
    navData_ = navData;
    invalidateCache();
    zoomToFit();
}

void MapWidget::setFilterManager(std::shared_ptr<FilterManager> filterManager) {
    if (filterManager_) {
        disconnect(filterManager_.get(), nullptr, this, nullptr);
    }
    
    filterManager_ = filterManager;
    
    if (filterManager_) {
        connect(filterManager_.get(), &FilterManager::filtersApplied,
                this, &MapWidget::onFiltersApplied);
    }
    
    invalidateCache();
    update();
}

void MapWidget::zoomToFit() {
    if (!navData_) {
        return;
    }
    
    // Calculate bounds of all data
    double minLat = 90.0, maxLat = -90.0;
    double minLon = 180.0, maxLon = -180.0;
    bool hasData = false;
    
    // Check airports
    for (const auto& airport : navData_->getAirports()) {
        double lat = airport->getLatitude();
        double lon = airport->getLongitude();
        if (lat != 0.0 || lon != 0.0) {
            minLat = std::min(minLat, lat);
            maxLat = std::max(maxLat, lat);
            minLon = std::min(minLon, lon);
            maxLon = std::max(maxLon, lon);
            hasData = true;
        }
    }
    
    // Check waypoints
    for (const auto& waypoint : navData_->getWaypoints()) {
        double lat = waypoint->getLatitude();
        double lon = waypoint->getLongitude();
        if (lat != 0.0 || lon != 0.0) {
            minLat = std::min(minLat, lat);
            maxLat = std::max(maxLat, lat);
            minLon = std::min(minLon, lon);
            maxLon = std::max(maxLon, lon);
            hasData = true;
        }
    }
    
    if (hasData) {
        converter_->zoomToFit(minLat, maxLat, minLon, maxLon, 0.1);
        invalidateCache();
        update();
    }
}

void MapWidget::zoomIn() {
    converter_->zoomIn(ZOOM_FACTOR);
    invalidateCache();
    update();
}

void MapWidget::zoomOut() {
    converter_->zoomOut(ZOOM_FACTOR);
    invalidateCache();
    update();
}

void MapWidget::resetView() {
    zoomToFit();
}

void MapWidget::onFiltersApplied() {
    invalidateCache();
    update();
}

void MapWidget::onDataChanged() {
    invalidateCache();
    zoomToFit();
}

void MapWidget::paintEvent(QPaintEvent* event) {
    Q_UNUSED(event)
    
    QPainter painter(this);
    painter.setRenderHint(QPainter::Antialiasing);
    
    // Update visible data if needed
    updateVisibleData();
    
    // Draw map elements
    drawBackground(painter);
    drawGrid(painter);
    
    if (showAirports_) {
        drawAirports(painter);
    }
    
    if (showWaypoints_) {
        drawWaypoints(painter);
    }
    
    if (showProcedures_) {
        drawProcedures(painter);
    }
    
    if (showRouteLines_) {
        drawRouteLines(painter);
    }
}

void MapWidget::mousePressEvent(QMouseEvent* event) {
    if (event->button() == Qt::LeftButton) {
        isDragging_ = true;
        lastMousePos_ = event->pos();
        dragStartPos_ = event->pos();
        setCursor(Qt::ClosedHandCursor);
    }
}

void MapWidget::mouseMoveEvent(QMouseEvent* event) {
    if (isDragging_) {
        QPoint delta = event->pos() - lastMousePos_;
        converter_->pan(-delta.x(), -delta.y());
        lastMousePos_ = event->pos();
        
        invalidateCache();
        if (!updateTimer_->isActive()) {
            updateTimer_->start();
        }
    }
}

void MapWidget::mouseReleaseEvent(QMouseEvent* event) {
    if (event->button() == Qt::LeftButton && isDragging_) {
        isDragging_ = false;
        setCursor(Qt::ArrowCursor);
        
        // Check for click (small movement)
        QPoint delta = event->pos() - dragStartPos_;
        if (delta.manhattanLength() < 5) {
            // Handle click
            auto airport = findAirportAt(event->pos());
            if (airport) {
                emit airportClicked(QString::fromStdString(airport->getIcaoCode()));
                return;
            }
            
            auto waypoint = findWaypointAt(event->pos());
            if (waypoint) {
                emit waypointClicked(QString::fromStdString(waypoint->getIdentifier()));
                return;
            }
            
            auto procedure = findProcedureAt(event->pos());
            if (procedure) {
                emit procedureClicked(QString::fromStdString(procedure->getIdentifier()));
                return;
            }
        }
        
        update();
    }
}

void MapWidget::wheelEvent(QWheelEvent* event) {
    double factor = (event->angleDelta().y() > 0) ? ZOOM_FACTOR : (1.0 / ZOOM_FACTOR);
    
    if (factor > 1.0) {
        converter_->zoomIn(factor);
    } else {
        converter_->zoomOut(1.0 / factor);
    }
    
    invalidateCache();
    update();
}

void MapWidget::resizeEvent(QResizeEvent* event) {
    Q_UNUSED(event)
    setupCoordinateConverter();
    invalidateCache();
}

void MapWidget::onUpdateTimer() {
    update();
}

void MapWidget::drawBackground(QPainter& painter) {
    painter.fillRect(rect(), QColor(240, 248, 255)); // Light blue background
}

void MapWidget::drawGrid(QPainter& painter) {
    painter.setPen(QPen(QColor(200, 200, 200), 1, Qt::DotLine));

    // Draw a simple grid
    int gridSpacing = 50;
    for (int x = 0; x < width(); x += gridSpacing) {
        painter.drawLine(x, 0, x, height());
    }
    for (int y = 0; y < height(); y += gridSpacing) {
        painter.drawLine(0, y, width(), y);
    }
}

void MapWidget::drawAirports(QPainter& painter) {
    painter.setPen(QPen(Qt::blue, 2));
    painter.setBrush(QBrush(Qt::blue));

    for (const auto& airport : visibleAirports_) {
        QPointF screenPos = getScreenPosition(airport->getLatitude(), airport->getLongitude());

        // Draw airport symbol (square)
        QRectF rect(screenPos.x() - AIRPORT_SYMBOL_SIZE/2,
                   screenPos.y() - AIRPORT_SYMBOL_SIZE/2,
                   AIRPORT_SYMBOL_SIZE, AIRPORT_SYMBOL_SIZE);
        painter.drawRect(rect);

        // Draw label
        painter.setPen(Qt::black);
        painter.drawText(screenPos + QPointF(AIRPORT_SYMBOL_SIZE, -AIRPORT_SYMBOL_SIZE),
                        QString::fromStdString(airport->getIcaoCode()));
        painter.setPen(QPen(Qt::blue, 2));
    }
}

void MapWidget::drawWaypoints(QPainter& painter) {
    painter.setPen(QPen(Qt::darkGreen, 1));
    painter.setBrush(QBrush(Qt::green));

    for (const auto& waypoint : visibleWaypoints_) {
        QPointF screenPos = getScreenPosition(waypoint->getLatitude(), waypoint->getLongitude());

        // Draw waypoint symbol (circle)
        painter.drawEllipse(screenPos, WAYPOINT_SYMBOL_SIZE/2, WAYPOINT_SYMBOL_SIZE/2);

        // Draw label for important waypoints
        if (waypoint->getType().find("R") != std::string::npos) { // RNAV waypoints
            painter.setPen(Qt::black);
            painter.drawText(screenPos + QPointF(WAYPOINT_SYMBOL_SIZE, 0),
                            QString::fromStdString(waypoint->getIdentifier()));
            painter.setPen(QPen(Qt::darkGreen, 1));
        }
    }
}

void MapWidget::drawProcedures(QPainter& painter) {
    painter.setPen(QPen(Qt::red, 1));
    painter.setBrush(QBrush(Qt::red));

    for (const auto& procedure : visibleProcedures_) {
        // Find the waypoint for this procedure's fix
        if (navData_) {
            auto waypoint = navData_->findWaypoint(procedure->getFixIdentifier());
            if (waypoint) {
                QPointF screenPos = getScreenPosition(waypoint->getLatitude(), waypoint->getLongitude());

                // Draw procedure symbol (triangle)
                QPolygonF triangle;
                triangle << QPointF(screenPos.x(), screenPos.y() - PROCEDURE_SYMBOL_SIZE)
                        << QPointF(screenPos.x() - PROCEDURE_SYMBOL_SIZE, screenPos.y() + PROCEDURE_SYMBOL_SIZE)
                        << QPointF(screenPos.x() + PROCEDURE_SYMBOL_SIZE, screenPos.y() + PROCEDURE_SYMBOL_SIZE);
                painter.drawPolygon(triangle);
            }
        }
    }
}

void MapWidget::drawRouteLines(QPainter& painter) {
    if (!showRouteLines_ || !navData_) {
        return;
    }

    // Group procedures by identifier and transition
    std::map<std::string, std::vector<std::shared_ptr<SIDSTARApproach>>> procedureGroups;

    for (const auto& procedure : visibleProcedures_) {
        std::string key = procedure->getIdentifier() + "|" + procedure->getTransitionIdentifier();
        procedureGroups[key].push_back(procedure);
    }

    // Draw route lines for each procedure group
    painter.setPen(QPen(Qt::magenta, 2));

    for (const auto& group : procedureGroups) {
        drawProcedureRoute(painter, group.second);
    }
}

void MapWidget::drawProcedureRoute(QPainter& painter, std::shared_ptr<SIDSTARApproach> procedure) {
    Q_UNUSED(painter)
    Q_UNUSED(procedure)
    // This function signature was changed to match the header
    // Implementation would go here for drawing individual procedure routes
}

void MapWidget::drawProcedureRoute(QPainter& painter, const std::vector<std::shared_ptr<SIDSTARApproach>>& procedures) {
    if (procedures.empty()) {
        return;
    }

    // Sort procedures by sequence number
    auto sortedProcedures = procedures;
    std::sort(sortedProcedures.begin(), sortedProcedures.end(),
              [](const auto& a, const auto& b) {
                  return a->getSequenceNumber() < b->getSequenceNumber();
              });

    // Draw lines connecting the waypoints in sequence
    QPointF lastPoint;
    bool hasLastPoint = false;

    for (const auto& procedure : sortedProcedures) {
        auto waypoint = navData_->findWaypoint(procedure->getFixIdentifier());
        if (waypoint) {
            QPointF currentPoint = getScreenPosition(waypoint->getLatitude(), waypoint->getLongitude());

            if (hasLastPoint) {
                painter.drawLine(lastPoint, currentPoint);
            }

            lastPoint = currentPoint;
            hasLastPoint = true;
        }
    }
}

void MapWidget::updateVisibleData() {
    if (cacheValid_) {
        return;
    }

    visibleAirports_.clear();
    visibleWaypoints_.clear();
    visibleProcedures_.clear();

    if (filterManager_) {
        visibleAirports_ = filterManager_->getFilteredAirports();
        visibleWaypoints_ = filterManager_->getAllRelatedWaypoints();
        visibleProcedures_ = filterManager_->getFilteredProcedures();
    } else if (navData_) {
        visibleAirports_ = navData_->getAirports();
        visibleWaypoints_ = navData_->getWaypoints();
        visibleProcedures_ = navData_->getSIDSTARApproaches();
    }

    cacheValid_ = true;
}

void MapWidget::invalidateCache() {
    cacheValid_ = false;
}

void MapWidget::setupCoordinateConverter() {
    QRectF viewport(0, 0, width(), height());
    converter_->setViewport(viewport);
}

QPointF MapWidget::getScreenPosition(double latitude, double longitude) const {
    return converter_->geoToScreen(latitude, longitude);
}

QPointF MapWidget::getGeoPosition(const QPoint& screenPos) const {
    return converter_->screenToGeo(screenPos.x(), screenPos.y());
}

std::shared_ptr<Airport> MapWidget::findAirportAt(const QPoint& pos) const {
    for (const auto& airport : visibleAirports_) {
        QPointF screenPos = getScreenPosition(airport->getLatitude(), airport->getLongitude());
        QPointF delta = screenPos - QPointF(pos);
        if (std::sqrt(delta.x() * delta.x() + delta.y() * delta.y()) <= HIT_TEST_RADIUS) {
            return airport;
        }
    }
    return nullptr;
}

std::shared_ptr<Waypoint> MapWidget::findWaypointAt(const QPoint& pos) const {
    for (const auto& waypoint : visibleWaypoints_) {
        QPointF screenPos = getScreenPosition(waypoint->getLatitude(), waypoint->getLongitude());
        QPointF delta = screenPos - QPointF(pos);
        if (std::sqrt(delta.x() * delta.x() + delta.y() * delta.y()) <= HIT_TEST_RADIUS) {
            return waypoint;
        }
    }
    return nullptr;
}

std::shared_ptr<SIDSTARApproach> MapWidget::findProcedureAt(const QPoint& pos) const {
    for (const auto& procedure : visibleProcedures_) {
        if (navData_) {
            auto waypoint = navData_->findWaypoint(procedure->getFixIdentifier());
            if (waypoint) {
                QPointF screenPos = getScreenPosition(waypoint->getLatitude(), waypoint->getLongitude());
                QPointF delta = screenPos - QPointF(pos);
                if (std::sqrt(delta.x() * delta.x() + delta.y() * delta.y()) <= HIT_TEST_RADIUS) {
                    return procedure;
                }
            }
        }
    }
    return nullptr;
}

} // namespace arinc424

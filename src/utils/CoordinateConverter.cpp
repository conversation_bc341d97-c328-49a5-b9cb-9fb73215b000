#include "utils/CoordinateConverter.h"
#include <cmath>
#include <algorithm>

namespace arinc424 {
namespace utils {

void CoordinateConverter::setViewport(const QRectF& viewport) {
    viewport_ = viewport;
    updateScale();
}

void CoordinateConverter::setGeographicBounds(double minLat, double maxLat, double minLon, double maxLon) {
    geoBounds_ = QRectF(minLon, minLat, maxLon - minLon, maxLat - minLat);
    updateScale();
}

void CoordinateConverter::setGeographicBounds(const QRectF& bounds) {
    geoBounds_ = bounds;
    updateScale();
}

QPointF CoordinateConverter::geoToScreen(double latitude, double longitude) const {
    if (geoBounds_.width() == 0 || geoBounds_.height() == 0) {
        return QPointF(0, 0);
    }
    
    // Normalize to 0-1 range
    double normalizedX = (longitude - geoBounds_.left()) / geoBounds_.width();
    double normalizedY = (latitude - geoBounds_.top()) / geoBounds_.height();
    
    // Convert to screen coordinates (note: Y is flipped for screen coordinates)
    double screenX = viewport_.left() + normalizedX * viewport_.width();
    double screenY = viewport_.bottom() - normalizedY * viewport_.height();
    
    return QPointF(screenX, screenY);
}

QPointF CoordinateConverter::geoToScreen(const QPointF& geoPoint) const {
    return geoToScreen(geoPoint.y(), geoPoint.x()); // QPointF is (x=lon, y=lat)
}

QPointF CoordinateConverter::screenToGeo(const QPointF& screenPoint) const {
    return screenToGeo(screenPoint.x(), screenPoint.y());
}

QPointF CoordinateConverter::screenToGeo(double x, double y) const {
    if (viewport_.width() == 0 || viewport_.height() == 0) {
        return QPointF(0, 0);
    }
    
    // Normalize to 0-1 range
    double normalizedX = (x - viewport_.left()) / viewport_.width();
    double normalizedY = (viewport_.bottom() - y) / viewport_.height(); // Y is flipped
    
    // Convert to geographic coordinates
    double longitude = geoBounds_.left() + normalizedX * geoBounds_.width();
    double latitude = geoBounds_.top() + normalizedY * geoBounds_.height();
    
    return QPointF(longitude, latitude); // QPointF is (x=lon, y=lat)
}

double CoordinateConverter::getScale() const {
    return std::min(scaleX_, scaleY_);
}

bool CoordinateConverter::isInBounds(double latitude, double longitude) const {
    return longitude >= geoBounds_.left() && longitude <= geoBounds_.right() &&
           latitude >= geoBounds_.top() && latitude <= geoBounds_.bottom();
}

bool CoordinateConverter::isInBounds(const QPointF& geoPoint) const {
    return isInBounds(geoPoint.y(), geoPoint.x()); // QPointF is (x=lon, y=lat)
}

double CoordinateConverter::calculateDistance(double lat1, double lon1, double lat2, double lon2) {
    const double R = 6371.0; // Earth's radius in kilometers
    
    // Convert to radians
    double lat1Rad = lat1 * M_PI / 180.0;
    double lon1Rad = lon1 * M_PI / 180.0;
    double lat2Rad = lat2 * M_PI / 180.0;
    double lon2Rad = lon2 * M_PI / 180.0;
    
    // Haversine formula
    double dLat = lat2Rad - lat1Rad;
    double dLon = lon2Rad - lon1Rad;
    
    double a = std::sin(dLat / 2) * std::sin(dLat / 2) +
               std::cos(lat1Rad) * std::cos(lat2Rad) *
               std::sin(dLon / 2) * std::sin(dLon / 2);
    
    double c = 2 * std::atan2(std::sqrt(a), std::sqrt(1 - a));
    
    return R * c;
}

double CoordinateConverter::calculateDistance(const QPointF& point1, const QPointF& point2) {
    return calculateDistance(point1.y(), point1.x(), point2.y(), point2.x());
}

double CoordinateConverter::calculateBearing(double lat1, double lon1, double lat2, double lon2) {
    // Convert to radians
    double lat1Rad = lat1 * M_PI / 180.0;
    double lat2Rad = lat2 * M_PI / 180.0;
    double dLon = (lon2 - lon1) * M_PI / 180.0;
    
    double y = std::sin(dLon) * std::cos(lat2Rad);
    double x = std::cos(lat1Rad) * std::sin(lat2Rad) -
               std::sin(lat1Rad) * std::cos(lat2Rad) * std::cos(dLon);
    
    double bearing = std::atan2(y, x) * 180.0 / M_PI;
    
    // Normalize to 0-360 degrees
    return std::fmod(bearing + 360.0, 360.0);
}

double CoordinateConverter::calculateBearing(const QPointF& point1, const QPointF& point2) {
    return calculateBearing(point1.y(), point1.x(), point2.y(), point2.x());
}

void CoordinateConverter::zoomIn(double factor) {
    double newWidth = geoBounds_.width() / factor;
    double newHeight = geoBounds_.height() / factor;
    
    double centerLon = geoBounds_.center().x();
    double centerLat = geoBounds_.center().y();
    
    geoBounds_ = QRectF(centerLon - newWidth / 2, centerLat - newHeight / 2, newWidth, newHeight);
    updateScale();
}

void CoordinateConverter::zoomOut(double factor) {
    zoomIn(1.0 / factor);
}

void CoordinateConverter::zoomToFit(double minLat, double maxLat, double minLon, double maxLon, double margin) {
    double latSpan = maxLat - minLat;
    double lonSpan = maxLon - minLon;
    
    // Add margin
    double latMargin = latSpan * margin;
    double lonMargin = lonSpan * margin;
    
    setGeographicBounds(minLat - latMargin, maxLat + latMargin, minLon - lonMargin, maxLon + lonMargin);
}

void CoordinateConverter::pan(double deltaX, double deltaY) {
    // Convert screen delta to geographic delta
    double geoDeltalon = deltaX * geoBounds_.width() / viewport_.width();
    double geoDeltaLat = -deltaY * geoBounds_.height() / viewport_.height(); // Y is flipped
    
    geoBounds_.translate(geoDeltalon, geoDeltaLat);
    updateScale();
}

void CoordinateConverter::updateScale() {
    if (viewport_.width() > 0 && viewport_.height() > 0 && 
        geoBounds_.width() > 0 && geoBounds_.height() > 0) {
        scaleX_ = viewport_.width() / geoBounds_.width();
        scaleY_ = viewport_.height() / geoBounds_.height();
    }
}

} // namespace utils
} // namespace arinc424

#include "utils/GeometryUtils.h"
#include "models/NavigationData.h"
#include "models/Waypoint.h"
#include "models/SIDSTARApproach.h"
#include "utils/CoordinateConverter.h"
#include <algorithm>
#include <cmath>

namespace arinc424 {
namespace utils {

QPolygonF GeometryUtils::buildRoute(const std::vector<std::shared_ptr<SIDSTARApproach>>& procedures,
                                   const NavigationData& navData) {
    QPolygonF route;
    
    if (procedures.empty()) {
        return route;
    }
    
    // Sort procedures by sequence number
    auto sortedProcedures = procedures;
    std::sort(sortedProcedures.begin(), sortedProcedures.end(),
              [](const auto& a, const auto& b) {
                  return a->getSequenceNumber() < b->getSequenceNumber();
              });
    
    // Build route from waypoints
    for (const auto& procedure : sortedProcedures) {
        auto waypoint = navData.findWaypoint(procedure->getFixIdentifier());
        if (waypoint && isValidCoordinate(waypoint->getLatitude(), waypoint->getLongitude())) {
            route.append(toQPointF(waypoint->getLatitude(), waypoint->getLongitude()));
        }
    }
    
    return route;
}

QPolygonF GeometryUtils::buildRouteFromWaypoints(const std::vector<std::shared_ptr<Waypoint>>& waypoints) {
    QPolygonF route;
    
    for (const auto& waypoint : waypoints) {
        if (waypoint && isValidCoordinate(waypoint->getLatitude(), waypoint->getLongitude())) {
            route.append(toQPointF(waypoint->getLatitude(), waypoint->getLongitude()));
        }
    }
    
    return route;
}

double GeometryUtils::calculateRouteLength(const QPolygonF& route) {
    double totalLength = 0.0;
    
    for (int i = 1; i < route.size(); ++i) {
        QPointF p1 = route[i - 1];
        QPointF p2 = route[i];
        
        // Convert QPointF (lon, lat) to lat, lon for distance calculation
        totalLength += CoordinateConverter::calculateDistance(p1.y(), p1.x(), p2.y(), p2.x());
    }
    
    return totalLength;
}

double GeometryUtils::calculateTotalDistance(const std::vector<std::shared_ptr<Waypoint>>& waypoints) {
    double totalDistance = 0.0;
    
    for (size_t i = 1; i < waypoints.size(); ++i) {
        auto wp1 = waypoints[i - 1];
        auto wp2 = waypoints[i];
        
        if (wp1 && wp2 && 
            isValidCoordinate(wp1->getLatitude(), wp1->getLongitude()) &&
            isValidCoordinate(wp2->getLatitude(), wp2->getLongitude())) {
            
            totalDistance += CoordinateConverter::calculateDistance(
                wp1->getLatitude(), wp1->getLongitude(),
                wp2->getLatitude(), wp2->getLongitude()
            );
        }
    }
    
    return totalDistance;
}

QPointF GeometryUtils::interpolatePoint(const QPointF& p1, const QPointF& p2, double ratio) {
    return QPointF(
        p1.x() + ratio * (p2.x() - p1.x()),
        p1.y() + ratio * (p2.y() - p1.y())
    );
}

QPolygonF GeometryUtils::smoothRoute(const QPolygonF& route, int iterations) {
    if (route.size() < 3) {
        return route;
    }
    
    QPolygonF smoothed = route;
    
    for (int iter = 0; iter < iterations; ++iter) {
        QPolygonF newRoute;
        newRoute.append(smoothed.first()); // Keep first point
        
        for (int i = 1; i < smoothed.size() - 1; ++i) {
            // Simple smoothing: average with neighbors
            QPointF prev = smoothed[i - 1];
            QPointF curr = smoothed[i];
            QPointF next = smoothed[i + 1];
            
            QPointF smoothedPoint(
                (prev.x() + 2 * curr.x() + next.x()) / 4.0,
                (prev.y() + 2 * curr.y() + next.y()) / 4.0
            );
            
            newRoute.append(smoothedPoint);
        }
        
        newRoute.append(smoothed.last()); // Keep last point
        smoothed = newRoute;
    }
    
    return smoothed;
}

QPolygonF GeometryUtils::simplifyRoute(const QPolygonF& route, double tolerance) {
    if (route.size() <= 2) {
        return route;
    }
    
    // Simple Douglas-Peucker-like algorithm
    QPolygonF simplified;
    simplified.append(route.first());
    
    QPointF lastPoint = route.first();
    
    for (int i = 1; i < route.size(); ++i) {
        QPointF currentPoint = route[i];
        
        // Calculate distance from last added point
        double distance = std::sqrt(
            std::pow(currentPoint.x() - lastPoint.x(), 2) +
            std::pow(currentPoint.y() - lastPoint.y(), 2)
        );
        
        if (distance > tolerance || i == route.size() - 1) {
            simplified.append(currentPoint);
            lastPoint = currentPoint;
        }
    }
    
    return simplified;
}

QRectF GeometryUtils::calculateBounds(const std::vector<std::shared_ptr<Waypoint>>& waypoints) {
    if (waypoints.empty()) {
        return QRectF();
    }
    
    double minLat = 90.0, maxLat = -90.0;
    double minLon = 180.0, maxLon = -180.0;
    bool hasValidPoint = false;
    
    for (const auto& waypoint : waypoints) {
        if (waypoint && isValidCoordinate(waypoint->getLatitude(), waypoint->getLongitude())) {
            double lat = waypoint->getLatitude();
            double lon = waypoint->getLongitude();
            
            minLat = std::min(minLat, lat);
            maxLat = std::max(maxLat, lat);
            minLon = std::min(minLon, lon);
            maxLon = std::max(maxLon, lon);
            hasValidPoint = true;
        }
    }
    
    if (!hasValidPoint) {
        return QRectF();
    }
    
    return QRectF(minLon, minLat, maxLon - minLon, maxLat - minLat);
}

QRectF GeometryUtils::calculateBounds(const QPolygonF& route) {
    if (route.isEmpty()) {
        return QRectF();
    }
    
    return route.boundingRect();
}

bool GeometryUtils::isValidCoordinate(double latitude, double longitude) {
    return latitude >= -90.0 && latitude <= 90.0 &&
           longitude >= -180.0 && longitude <= 180.0 &&
           (latitude != 0.0 || longitude != 0.0); // Exclude null island
}

QPointF GeometryUtils::toQPointF(double latitude, double longitude) {
    return QPointF(longitude, latitude); // QPointF is (x=lon, y=lat)
}

std::pair<double, double> GeometryUtils::fromQPointF(const QPointF& point) {
    return std::make_pair(point.y(), point.x()); // Returns (lat, lon)
}

} // namespace utils
} // namespace arinc424

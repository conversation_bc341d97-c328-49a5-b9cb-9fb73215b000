#!/usr/bin/env python3
"""
ARINC-424 to JSO<PERSON> Converter

This script parses ARINC-424 navigation data files and outputs the parsed data as JSON.
It can process individual records, entire files, or directories containing multiple files.

Usage:
    python3 arinc424_to_json.py --record "SUSAP KSEAK1ASEA..."  # Parse single record
    python3 arinc424_to_json.py --file data/airport             # Parse single file
    python3 arinc424_to_json.py --directory data/ARINC-424-18   # Parse all files in directory
    python3 arinc424_to_json.py --file data/airport --output output.json  # Save to file
"""

import argparse
import json
import os
import sys
from typing import List, Dict, Any, Optional
import arinc424


def parse_record_to_dict(line: str) -> Optional[Dict[str, Any]]:
    """
    Parse a single ARINC-424 record and return as dictionary.
    
    Args:
        line: ARINC-424 record string
        
    Returns:
        Dictionary containing parsed record data, or None if parsing failed
    """
    record = arinc424.Record()
    if record.read(line):
        # Get the JSON data and parse it back to a dictionary
        json_str = record.json(output=False)
        return json.loads(json_str)
    return None


def parse_file_to_json(file_path: str, pretty_print: bool = True) -> List[Dict[str, Any]]:
    """
    Parse an entire ARINC-424 file and return as list of dictionaries.
    
    Args:
        file_path: Path to the ARINC-424 file
        pretty_print: Whether to print progress information
        
    Returns:
        List of dictionaries containing parsed record data
    """
    records = []
    
    if not os.path.exists(file_path):
        print(f"Error: File '{file_path}' not found", file=sys.stderr)
        return records
    
    try:
        with open(file_path, 'r') as f:
            lines = f.readlines()
            
        if pretty_print:
            print(f"Processing file: {file_path}")
            print(f"Total lines: {len(lines)}")
        
        parsed_count = 0
        for line_num, line in enumerate(lines, 1):
            line = line.strip()
            if not line or line.startswith('*'):  # Skip empty lines and comments
                continue
                
            record_data = parse_record_to_dict(line)
            if record_data:
                # Add metadata about the source
                record_data['_source_file'] = os.path.basename(file_path)
                record_data['_line_number'] = line_num
                record_data['_raw_record'] = line
                records.append(record_data)
                parsed_count += 1
            elif pretty_print:
                print(f"Warning: Failed to parse line {line_num}: {line[:50]}...")
        
        if pretty_print:
            print(f"Successfully parsed {parsed_count} records")
            
    except Exception as e:
        print(f"Error reading file '{file_path}': {e}", file=sys.stderr)
    
    return records


def parse_directory_to_json(directory_path: str, pretty_print: bool = True) -> Dict[str, List[Dict[str, Any]]]:
    """
    Parse all ARINC-424 files in a directory and return as nested dictionary.
    
    Args:
        directory_path: Path to directory containing ARINC-424 files
        pretty_print: Whether to print progress information
        
    Returns:
        Dictionary with filenames as keys and lists of parsed records as values
    """
    all_records = {}
    
    if not os.path.exists(directory_path):
        print(f"Error: Directory '{directory_path}' not found", file=sys.stderr)
        return all_records
    
    if not os.path.isdir(directory_path):
        print(f"Error: '{directory_path}' is not a directory", file=sys.stderr)
        return all_records
    
    try:
        files = [f for f in os.listdir(directory_path) 
                if os.path.isfile(os.path.join(directory_path, f)) and not f.startswith('.')]
        
        if pretty_print:
            print(f"Processing directory: {directory_path}")
            print(f"Found {len(files)} files")
        
        for filename in sorted(files):
            file_path = os.path.join(directory_path, filename)
            records = parse_file_to_json(file_path, pretty_print=False)
            if records:
                all_records[filename] = records
                if pretty_print:
                    print(f"  {filename}: {len(records)} records")
            elif pretty_print:
                print(f"  {filename}: No valid records found")
                
    except Exception as e:
        print(f"Error processing directory '{directory_path}': {e}", file=sys.stderr)
    
    return all_records


def save_json_to_file(data: Any, output_path: str, pretty_print: bool = True) -> bool:
    """
    Save data to JSON file.
    
    Args:
        data: Data to save
        output_path: Output file path
        pretty_print: Whether to format JSON nicely
        
    Returns:
        True if successful, False otherwise
    """
    try:
        with open(output_path, 'w') as f:
            if pretty_print:
                json.dump(data, f, indent=2, sort_keys=True)
            else:
                json.dump(data, f)
        return True
    except Exception as e:
        print(f"Error saving to '{output_path}': {e}", file=sys.stderr)
        return False


def main():
    parser = argparse.ArgumentParser(
        description="Parse ARINC-424 navigation data and output as JSON",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Parse a single record
  python3 arinc424_to_json.py --record "SUSAP KSEAK1ASEA     110000119Y N47265700W122182910E019900429250SEA K11800018000CU00Y NAS    SEATTLE-TACOMA INTL           045698808"
  
  # Parse a single file and print to stdout
  python3 arinc424_to_json.py --file data/ARINC-424-18/airport
  
  # Parse a single file and save to JSON file
  python3 arinc424_to_json.py --file data/ARINC-424-18/airport --output airport_data.json
  
  # Parse all files in a directory
  python3 arinc424_to_json.py --directory data/ARINC-424-18 --output all_data.json
  
  # Parse with compact JSON output
  python3 arinc424_to_json.py --file data/ARINC-424-18/airport --compact
        """
    )
    
    # Input options (mutually exclusive)
    input_group = parser.add_mutually_exclusive_group(required=True)
    input_group.add_argument('--record', '-r', 
                           help='Parse a single ARINC-424 record string')
    input_group.add_argument('--file', '-f', 
                           help='Parse an ARINC-424 file')
    input_group.add_argument('--directory', '-d', 
                           help='Parse all ARINC-424 files in a directory')
    
    # Output options
    parser.add_argument('--output', '-o', 
                       help='Output JSON file path (default: print to stdout)')
    parser.add_argument('--compact', '-c', action='store_true',
                       help='Output compact JSON (no pretty printing)')
    parser.add_argument('--quiet', '-q', action='store_true',
                       help='Suppress progress messages')
    
    args = parser.parse_args()
    
    # Determine output settings
    pretty_json = not args.compact
    verbose = not args.quiet
    
    # Process input based on type
    result_data = None
    
    if args.record:
        # Parse single record
        if verbose:
            print("Parsing single record...")
        result_data = parse_record_to_dict(args.record)
        if result_data is None:
            print("Error: Failed to parse the provided record", file=sys.stderr)
            sys.exit(1)
            
    elif args.file:
        # Parse single file
        result_data = parse_file_to_json(args.file, verbose)
        if not result_data:
            print("Error: No valid records found in file", file=sys.stderr)
            sys.exit(1)
            
    elif args.directory:
        # Parse directory
        result_data = parse_directory_to_json(args.directory, verbose)
        if not result_data:
            print("Error: No valid records found in directory", file=sys.stderr)
            sys.exit(1)
    
    # Output results
    if args.output:
        # Save to file
        if verbose:
            print(f"Saving results to: {args.output}")
        if save_json_to_file(result_data, args.output, pretty_json):
            if verbose:
                print("Successfully saved JSON output")
        else:
            sys.exit(1)
    else:
        # Print to stdout
        if pretty_json:
            print(json.dumps(result_data, indent=2, sort_keys=True))
        else:
            print(json.dumps(result_data))


if __name__ == '__main__':
    main()

#!/usr/bin/env python3
"""
Example usage of the ARINC-424 library for parsing navigation data.

This script demonstrates various ways to use the arinc424 library
to parse aviation navigation data and convert it to JSON format.
"""

import json
import arinc424


def example_single_record():
    """Example: Parse a single ARINC-424 record"""
    print("=== Example 1: Parsing a Single Record ===")
    
    # Example airport record for Seattle-Tacoma International Airport
    record_line = "SUSAP KSEAK1ASEA     110000119Y N47265700W122182910E019900429250SEA K11800018000CU00Y NAS    SEATTLE-TACOMA INTL           045698808"
    
    # Create a record object and parse the line
    record = arinc424.Record()
    if record.read(record_line):
        print("✓ Record parsed successfully!")
        
        # Display as formatted table (default library output)
        print("\nFormatted table output:")
        record.decode()
        
        # Get JSON output
        print("\nJSON output:")
        json_data = record.json(output=False)  # output=False prevents printing
        parsed_data = json.loads(json_data)
        print(json.dumps(parsed_data, indent=2))
        
        # Access specific fields
        print(f"\nAirport: {parsed_data['Airport Name'].strip()}")
        print(f"ICAO Code: {parsed_data['Airport ICAO Identifier']}")
        print(f"Elevation: {parsed_data['Airport Elevation']} ft")
        
    else:
        print("✗ Failed to parse record")
    
    print("\n" + "="*60 + "\n")


def example_file_parsing():
    """Example: Parse an entire file"""
    print("=== Example 2: Parsing a File ===")
    
    file_path = "data/ARINC-424-18/airport"
    records = []
    
    try:
        with open(file_path, 'r') as f:
            lines = f.readlines()
        
        print(f"Processing file: {file_path}")
        print(f"Total lines: {len(lines)}")
        
        for line_num, line in enumerate(lines, 1):
            line = line.strip()
            if not line or line.startswith('*'):  # Skip empty lines and comments
                continue
            
            record = arinc424.Record()
            if record.read(line):
                json_data = record.json(output=False)
                record_dict = json.loads(json_data)
                record_dict['_line_number'] = line_num
                records.append(record_dict)
        
        print(f"✓ Successfully parsed {len(records)} records")
        
        # Display summary of parsed records
        print("\nParsed records summary:")
        for i, record in enumerate(records):
            airport_name = record.get('Airport Name', 'Unknown').strip()
            icao = record.get('Airport ICAO Identifier', 'Unknown')
            print(f"  {i+1}. {airport_name} ({icao})")
        
        # Save to JSON file
        output_file = "parsed_airports.json"
        with open(output_file, 'w') as f:
            json.dump(records, f, indent=2)
        print(f"\n✓ Saved {len(records)} records to {output_file}")
        
    except FileNotFoundError:
        print(f"✗ File not found: {file_path}")
    except Exception as e:
        print(f"✗ Error processing file: {e}")
    
    print("\n" + "="*60 + "\n")


def example_different_record_types():
    """Example: Parse different types of ARINC-424 records"""
    print("=== Example 3: Different Record Types ===")
    
    # Different types of records
    sample_records = {
        "Airport": "SUSAP KSEAK1ASEA     110000119Y N47265700W122182910E019900429250SEA K11800018000CU00Y NAS    SEATTLE-TACOMA INTL           045698808",
        "VHF Navaid": "SUSAD SEAK1ASEA     0                   N47265700W122182910E0199      116.80SEA   NAS                                  045698808",
        "Waypoint": "SUSAEA ALPSE K1A     0                   N47265700W122182910E0199                  NAS                                  045698808"
    }
    
    for record_type, record_line in sample_records.items():
        print(f"\n--- {record_type} Record ---")
        
        record = arinc424.Record()
        if record.read(record_line):
            json_data = record.json(output=False)
            parsed_data = json.loads(json_data)
            
            print(f"✓ Parsed {record_type} record")
            print(f"Record Type: {parsed_data.get('Record Type', 'Unknown')}")
            print(f"Section Code: {parsed_data.get('Section Code', 'Unknown')}")
            
            # Show a few key fields based on record type
            if record_type == "Airport":
                print(f"Airport: {parsed_data.get('Airport Name', 'Unknown').strip()}")
                print(f"ICAO: {parsed_data.get('Airport ICAO Identifier', 'Unknown')}")
            elif record_type == "VHF Navaid":
                print(f"Navaid ID: {parsed_data.get('VOR/DME/TACAN Identifier', 'Unknown')}")
                print(f"Frequency: {parsed_data.get('VOR/DME/TACAN Frequency', 'Unknown')}")
            elif record_type == "Waypoint":
                print(f"Waypoint: {parsed_data.get('Waypoint Identifier', 'Unknown')}")
        else:
            print(f"✗ Failed to parse {record_type} record")
    
    print("\n" + "="*60 + "\n")


def example_using_library_functions():
    """Example: Using the library's built-in functions"""
    print("=== Example 4: Using Library Functions ===")
    
    # Using the parse function (prints to console)
    print("Using arinc424.parse() function:")
    record_line = "SUSAP KSEAK1ASEA     110000119Y N47265700W122182910E019900429250SEA K11800018000CU00Y NAS    SEATTLE-TACOMA INTL           045698808"
    success = arinc424.parse(record_line, output=False)  # output=False to suppress table
    print(f"Parse result: {'Success' if success else 'Failed'}")
    
    # Using the read_file function
    print(f"\nUsing arinc424.read_file() function:")
    print("(This will print formatted tables for all records in the file)")
    # Uncomment the next line to see the formatted output:
    # arinc424.read_file("data/ARINC-424-18/airport", output=True)
    print("(Commented out to avoid cluttering the output)")
    
    print("\n" + "="*60 + "\n")


def main():
    """Run all examples"""
    print("ARINC-424 Library Usage Examples")
    print("=" * 60)
    print()
    
    # Run all examples
    example_single_record()
    example_file_parsing()
    example_different_record_types()
    example_using_library_functions()
    
    print("All examples completed!")
    print("\nFor more advanced usage, see the arinc424_to_json.py script")
    print("which provides a command-line interface for batch processing.")


if __name__ == '__main__':
    main()

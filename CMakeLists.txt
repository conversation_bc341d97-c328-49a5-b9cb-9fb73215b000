cmake_minimum_required(VERSION 3.16)
project(ARINC424MapViewer VERSION 1.0.0 LANGUAGES CXX)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Find required packages - try Qt6 first, fallback to Qt5
find_package(Qt6 COMPONENTS Core Widgets)
if(NOT Qt6_FOUND)
    find_package(Qt5 REQUIRED COMPONENTS Core Widgets)
    set(QT_VERSION_MAJOR 5)
else()
    set(QT_VERSION_MAJOR 6)
endif()

# Find nlohmann/json - try package first, then fallback to system headers
find_package(nlohmann_json 3.2.0 QUIET)
if(NOT nlohmann_json_FOUND)
    # Try to find system-installed nlohmann/json
    find_path(NLOHMANN_JSON_INCLUDE_DIR nlohmann/json.hpp
        PATHS /usr/include /usr/local/include /opt/homebrew/include
    )
    if(NLOHMANN_JSON_INCLUDE_DIR)
        message(STATUS "Found nlohmann/json headers at: ${NLOHMANN_JSON_INCLUDE_DIR}")
        set(NLOHMANN_JSON_FOUND TRUE)
    else()
        message(FATAL_ERROR "nlohmann/json not found. Please install libnlohmann-json3-dev or nlohmann-json")
    endif()
endif()

# Set up Qt
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTORCC ON)
set(CMAKE_AUTOUIC ON)

# Include directories
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/include)

# Source files
set(SOURCES
    src/main.cpp
    src/MainWindow.cpp
    src/DataManager.cpp
    src/FilterManager.cpp
    src/MapWidget.cpp
    src/ProcessManager.cpp
    src/models/Airport.cpp
    src/models/Waypoint.cpp
    src/models/SIDSTARApproach.cpp
    src/models/NavigationData.cpp
    src/utils/CoordinateConverter.cpp
    src/utils/GeometryUtils.cpp
)

# Header files
set(HEADERS
    include/MainWindow.h
    include/DataManager.h
    include/FilterManager.h
    include/MapWidget.h
    include/ProcessManager.h
    include/models/Airport.h
    include/models/Waypoint.h
    include/models/SIDSTARApproach.h
    include/models/NavigationData.h
    include/utils/CoordinateConverter.h
    include/utils/GeometryUtils.h
)

# Create executable
add_executable(${PROJECT_NAME} ${SOURCES} ${HEADERS})

# Link libraries
target_link_libraries(${PROJECT_NAME} 
    Qt6::Core 
    Qt6::Widgets
    nlohmann_json::nlohmann_json
)

# Compiler-specific options
if(MSVC)
    target_compile_options(${PROJECT_NAME} PRIVATE /W4)
else()
    target_compile_options(${PROJECT_NAME} PRIVATE -Wall -Wextra -Wpedantic)
endif()

# Install rules
install(TARGETS ${PROJECT_NAME}
    BUNDLE DESTINATION .
    LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
    RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
)

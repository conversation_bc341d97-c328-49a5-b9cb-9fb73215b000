# ARINC 424 Map Viewer

A C++ application for visualizing ARINC 424 navigation data on an interactive map with hierarchical filtering capabilities.

## Features

- **Interactive Map Display**: Pan, zoom, and click on navigation elements
- **Hierarchical Filtering**: Filter by Region → ICAO → Record Type
- **Multiple Data Types**: Airports, waypoints, SIDs, STARs, and approach procedures
- **Route Visualization**: Draw connecting lines for SID/STAR procedures through their waypoints
- **On-Demand Loading**: Generate route lines only when needed for performance
- **Cross-Referencing**: Automatically include waypoints referenced by filtered procedures

## Requirements

- Qt6 (Core, Widgets)
- nlohmann/json library
- CMake 3.16+
- C++17 compatible compiler

## Building

1. Install dependencies:
   ```bash
   # Ubuntu/Debian
   sudo apt install qt6-base-dev libnlohmann-json3-dev cmake build-essential
   
   # macOS with Homebrew
   brew install qt6 nlohmann-json cmake
   
   # Windows with vcpkg
   vcpkg install qt6 nlohmann-json
   ```

2. Build the application:
   ```bash
   mkdir build
   cd build
   cmake ..
   make
   ```

3. Run the application:
   ```bash
   ./ARINC424MapViewer
   ```

## Usage

### Loading Data

1. **From Menu**: File → Open JSON File...
2. **Command Line**: `./ARINC424MapViewer --file path/to/data.json`

The application expects JSON data generated by the Python ARINC 424 parser script.

### Filtering Data

The application provides a three-level hierarchical filtering system:

1. **Region**: Select a geographic region (e.g., "USA")
2. **ICAO**: Select an airport ICAO code (e.g., "KSEA")
3. **Record Types**: Select one or more record types:
   - Airport
   - Waypoint
   - SID (Standard Instrument Departure)
   - STAR (Standard Terminal Arrival Route)
   - Approach

### Map Controls

- **Pan**: Click and drag to move the map
- **Zoom**: Use mouse wheel or zoom buttons
- **Click Elements**: Click on airports, waypoints, or procedures for information
- **Display Options**: Toggle visibility of different element types

### Display Options

- **Show Airports**: Display airport symbols (blue squares)
- **Show Waypoints**: Display waypoint symbols (green circles)
- **Show Procedures**: Display procedure symbols (red triangles)
- **Show Route Lines**: Draw connecting lines for SID/STAR routes

## Data Format

The application reads JSON data with the following structure:

```json
{
  "airport": [...],
  "enroute_waypoint": [...],
  "terminal_waypoint": [...],
  "sids": [...],
  "stars": [...],
  "instrument_approaches": [...]
}
```

Each record should include coordinate information and relevant identifiers for cross-referencing.

## Architecture

### Core Components

- **NavigationData**: Main data container and parser
- **FilterManager**: Handles hierarchical filtering logic
- **MapWidget**: Interactive map display with Qt graphics
- **CoordinateConverter**: Geographic to screen coordinate conversion
- **MainWindow**: User interface and application control

### Data Models

- **Airport**: Airport information with coordinates and identifiers
- **Waypoint**: Navigation waypoints (enroute and terminal)
- **SIDSTARApproach**: Procedure definitions with waypoint references

### Key Features

1. **Single-Shot Updates**: Filtering triggers one update cycle, not continuous loops
2. **On-Demand Route Generation**: Route lines are generated only when procedures are visible
3. **Cross-Reference Resolution**: Automatically includes waypoints referenced by procedures
4. **Efficient Rendering**: Cached visible data with invalidation on filter changes

## Performance Considerations

- Large datasets are handled efficiently through lookup maps
- Visible data is cached and only updated when filters change
- Route generation is performed on-demand to avoid unnecessary computation
- Screen coordinate conversion is optimized for interactive performance

## Troubleshooting

### Common Issues

1. **Missing Dependencies**: Ensure Qt6 and nlohmann/json are properly installed
2. **JSON Parse Errors**: Verify the JSON file format matches expected structure
3. **No Data Visible**: Check that filters are not too restrictive
4. **Performance Issues**: Try filtering to smaller datasets for large files

### Debug Information

The application provides status information in the status bar showing:
- Current filter counts vs. total data counts
- Selected elements when clicked
- Loading status and error messages

## Example Usage

1. Load the `all_navigation_data.json` file generated by the Python script
2. Select "USA" region to see US navigation data
3. Select "KSEA" ICAO code to focus on Seattle-Tacoma International Airport
4. Select "SID" and "STAR" record types to see departure and arrival procedures
5. Enable "Show Route Lines" to visualize the procedure routes
6. Click on waypoints and procedures to see their identifiers

The application will automatically include all waypoints referenced by the selected SID/STAR procedures, even if they weren't directly selected in the filters.

## License

This project is provided as-is for educational and research purposes.

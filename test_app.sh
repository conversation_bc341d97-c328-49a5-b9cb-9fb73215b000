#!/bin/bash

# Test script for ARINC 424 Map Viewer
echo "Testing ARINC 424 Map Viewer..."

# Check if the executable exists
if [ ! -f "./build/ARINC424MapViewer" ]; then
    echo "Error: ARINC424MapViewer executable not found!"
    exit 1
fi

echo "✓ Executable found"

# Check if the JSON data file exists
if [ ! -f "./all_navigation_data.json" ]; then
    echo "Warning: all_navigation_data.json not found in current directory"
    echo "You can load a JSON file using the File menu in the application"
else
    echo "✓ JSON data file found"
fi

echo ""
echo "Application built successfully!"
echo ""
echo "To run the application:"
echo "  cd build"
echo "  ./ARINC424MapViewer"
echo ""
echo "Or to load a specific JSON file:"
echo "  ./ARINC424MapViewer --file ../all_navigation_data.json"
echo ""
echo "Features:"
echo "- Interactive map with pan and zoom"
echo "- Hierarchical filtering (Region → ICAO → Record Type)"
echo "- Display airports, waypoints, and procedures"
echo "- Route visualization for SID/STAR procedures"
echo "- Click on elements for information"
echo "- Direct ARINC .pc file processing"
echo "- Automatic JSON file management in processed_json/ subdirectory"
echo ""
echo "Controls:"
echo "- Left panel: Filters and display options"
echo "- Map: Click and drag to pan, mouse wheel to zoom"
echo "- Menu: File operations and view controls"
echo ""
echo "File Loading Options:"
echo "1. File → Open JSON File... (load existing JSON)"
echo "2. File → Open ARINC File (.pc)... (process and load ARINC file)"
echo "3. File → Browse Processed Files... (browse previously processed files)"
echo ""
echo "Processed files are automatically saved to: ./processed_json/"
